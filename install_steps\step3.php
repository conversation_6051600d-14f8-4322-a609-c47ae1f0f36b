<h3><i class="fas fa-router"></i> إعداد MikroTik</h3>
<p>يرجى إدخال بيانات الاتصال بجهاز MikroTik RouterOS.</p>

<form method="POST">
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">عنوان IP</label>
                <input type="text" name="mikrotik_host" class="form-control" value="***********" required>
                <div class="form-text">عنوان IP الخاص بجهاز المايكروتك</div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">رقم المنفذ</label>
                <input type="number" name="mikrotik_port" class="form-control" value="8728" min="1" max="65535" required>
                <div class="form-text">منفذ API (افتراضي: 8728)</div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">اسم المستخدم</label>
                <input type="text" name="mikrotik_user" class="form-control" value="admin" required>
                <div class="form-text">اسم المستخدم للدخول على المايكروتك</div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">كلمة المرور</label>
                <input type="password" name="mikrotik_pass" class="form-control" placeholder="كلمة مرور المايكروتك">
                <div class="form-text">اتركها فارغة إذا لم تكن محددة</div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>تأكد من:</strong>
        <ul class="mb-0 mt-2">
            <li>تفعيل API Service في المايكروتك</li>
            <li>إعداد Hotspot Server</li>
            <li>صحة بيانات الاتصال</li>
        </ul>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h6><i class="fas fa-terminal"></i> أوامر إعداد المايكروتك</h6>
        </div>
        <div class="card-body">
            <p><strong>لتفعيل API:</strong></p>
            <code>/ip service enable api</code><br>
            <code>/ip service set api port=8728</code>
            
            <p class="mt-3"><strong>لإنشاء مستخدم جديد (اختياري):</strong></p>
            <code>/user add name=hotspot-manager password=كلمة_مرور_قوية group=full</code>
            
            <p class="mt-3"><strong>لإعداد Hotspot:</strong></p>
            <code>/ip hotspot setup</code>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <a href="install.php?step=2" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> السابق
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-router"></i> حفظ إعدادات MikroTik
        </button>
    </div>
</form>

<div class="mt-4">
    <h5>معلومات مهمة:</h5>
    <div class="row">
        <div class="col-md-6">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-shield-alt"></i> الأمان
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li>• استخدم مستخدم محدود الصلاحيات</li>
                        <li>• غيّر المنفذ الافتراضي</li>
                        <li>• استخدم كلمة مرور قوية</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-check"></i> التحقق
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li>• تأكد من تشغيل API</li>
                        <li>• اختبر الاتصال</li>
                        <li>• تحقق من البروفايلات</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
