<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// معالجة إضافة بروفايل جديد
if ($_POST['action'] == 'add_profile') {
    $name = validateInput($_POST['name']);
    $display_name = validateInput($_POST['display_name']);
    $session_timeout = intval($_POST['session_timeout']);
    $idle_timeout = intval($_POST['idle_timeout']);
    $rate_limit = validateInput($_POST['rate_limit']);
    $shared_users = intval($_POST['shared_users']);
    
    if (addProfile($name, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users)) {
        $_SESSION['success'] = "تم إضافة البروفايل بنجاح";
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة البروفايل";
    }
}

// معالجة تحديث بروفايل
if ($_POST['action'] == 'update_profile') {
    $id = intval($_POST['id']);
    $display_name = validateInput($_POST['display_name']);
    $session_timeout = intval($_POST['session_timeout']);
    $idle_timeout = intval($_POST['idle_timeout']);
    $rate_limit = validateInput($_POST['rate_limit']);
    $shared_users = intval($_POST['shared_users']);
    
    if (updateProfile($id, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users)) {
        $_SESSION['success'] = "تم تحديث البروفايل بنجاح";
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء تحديث البروفايل";
    }
}

// معالجة حذف بروفايل
if ($_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = intval($_GET['id']);
    if (deleteProfile($id)) {
        $_SESSION['success'] = "تم حذف البروفايل بنجاح";
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء حذف البروفايل";
    }
}

// جلب جميع البروفايلات
$profiles = getAvailableProfiles();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البروفايلات - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="header-content">
                    <div class="logo-container">
                        <img src="assets/img/Logo.png" alt="MikroSys Logo" class="system-logo">
                    </div>
                    <div class="header-text">
                        <h1><i class="fas fa-tachometer-alt"></i> إدارة بروفايلات السرعة</h1>
                        <p class="mb-0">إعداد وإدارة بروفايلات الهوتسبوت</p>
                    </div>
                    <div class="header-actions">
                        <a href="sync_profiles.php" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-sync-alt"></i> مزامنة MikroTik
                        </a>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="container p-4">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <div class="row">
                    <!-- نموذج إضافة بروفايل جديد -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-plus-circle"></i> إضافة بروفايل جديد</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="addProfileForm">
                                    <input type="hidden" name="action" value="add_profile">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">اسم البروفايل (بالإنجليزية)</label>
                                        <input type="text" name="name" class="form-control" placeholder="مثال: 5mbps_2hours" required>
                                        <div class="form-text">بدون مسافات أو رموز خاصة</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">الاسم المعروض</label>
                                        <input type="text" name="display_name" class="form-control" placeholder="مثال: 5 ميجا - ساعتين" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">مدة الجلسة (ثانية)</label>
                                        <div class="input-group">
                                            <input type="number" name="session_timeout" class="form-control" value="7200" min="0">
                                            <button type="button" class="btn btn-outline-secondary" onclick="calculateTime('session')">
                                                <i class="fas fa-calculator"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">0 = بدون حد زمني</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">مهلة عدم النشاط (ثانية)</label>
                                        <input type="number" name="idle_timeout" class="form-control" value="300" min="0">
                                        <div class="form-text">0 = بدون مهلة</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">حد السرعة</label>
                                        <div class="input-group">
                                            <input type="text" name="rate_limit" class="form-control" placeholder="مثال: 5M/5M" value="">
                                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                قوالب
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="setRateLimit('1M/512K')">1M/512K - أساسي</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setRateLimit('2M/1M')">2M/1M - عادي</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setRateLimit('5M/2M')">5M/2M - جيد</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setRateLimit('10M/5M')">10M/5M - ممتاز</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setRateLimit('20M/10M')">20M/10M - عالي</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setRateLimit('')">بدون حد</a></li>
                                            </ul>
                                        </div>
                                        <div class="form-text">تنسيق: تحميل/رفع (مثال: 10M/2M)</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">عدد المستخدمين المتزامنين</label>
                                        <input type="number" name="shared_users" class="form-control" value="1" min="1">
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-plus"></i> إضافة البروفايل
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قائمة البروفايلات الموجودة -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-dark text-white">
                                <h5><i class="fas fa-list"></i> البروفايلات الموجودة</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>الاسم المعروض</th>
                                                <th>مدة الجلسة</th>
                                                <th>حد السرعة</th>
                                                <th>المستخدمين</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($profiles as $profile): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($profile['display_name']) ?></strong>
                                                        <br><small class="text-muted"><?= htmlspecialchars($profile['name']) ?></small>
                                                    </td>
                                                    <td>
                                                        <?php if ($profile['session_timeout'] == 0): ?>
                                                            <span class="badge bg-success">بدون حد</span>
                                                        <?php else: ?>
                                                            <?= formatTime($profile['session_timeout']) ?>
                                                        <?php endif; ?>
                                                        <?php if ($profile['idle_timeout'] > 0): ?>
                                                            <br><small class="text-muted">خمول: <?= formatTime($profile['idle_timeout']) ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (empty($profile['rate_limit'])): ?>
                                                            <span class="badge bg-info">بدون حد</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning"><?= htmlspecialchars($profile['rate_limit']) ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?= $profile['shared_users'] ?></span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="editProfile(<?= $profile['id'] ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProfile(<?= $profile['id'] ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مودال حاسبة الوقت -->
    <div class="modal fade" id="timeCalculatorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حاسبة الوقت</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">الأيام</label>
                            <input type="number" id="calc_days" class="form-control" value="0" min="0">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الساعات</label>
                            <input type="number" id="calc_hours" class="form-control" value="2" min="0" max="23">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الدقائق</label>
                            <input type="number" id="calc_minutes" class="form-control" value="0" min="0" max="59">
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">النتيجة بالثواني:</label>
                        <input type="text" id="calc_result" class="form-control" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="applyCalculatedTime()">تطبيق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/script.js"></script>
    <script>
        let currentTimeField = '';
        
        function calculateTime(field) {
            currentTimeField = field;
            const modal = new bootstrap.Modal(document.getElementById('timeCalculatorModal'));
            modal.show();
            updateCalculation();
        }
        
        function updateCalculation() {
            const days = parseInt(document.getElementById('calc_days').value) || 0;
            const hours = parseInt(document.getElementById('calc_hours').value) || 0;
            const minutes = parseInt(document.getElementById('calc_minutes').value) || 0;
            
            const totalSeconds = (days * 24 * 60 * 60) + (hours * 60 * 60) + (minutes * 60);
            document.getElementById('calc_result').value = totalSeconds;
        }
        
        function applyCalculatedTime() {
            const result = document.getElementById('calc_result').value;
            if (currentTimeField === 'session') {
                document.querySelector('input[name="session_timeout"]').value = result;
            }
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('timeCalculatorModal'));
            modal.hide();
        }
        
        function editProfile(id) {
            window.location.href = 'edit_profile.php?id=' + id;
        }
        
        function deleteProfile(id) {
            if (confirm('هل أنت متأكد من حذف هذا البروفايل؟')) {
                window.location.href = 'profiles.php?action=delete&id=' + id;
            }
        }

        function setRateLimit(value) {
            document.querySelector('input[name="rate_limit"]').value = value;
        }
        
        // تحديث الحساب عند تغيير القيم
        document.addEventListener('DOMContentLoaded', function() {
            ['calc_days', 'calc_hours', 'calc_minutes'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateCalculation);
            });
        });
    </script>
</body>
</html>
