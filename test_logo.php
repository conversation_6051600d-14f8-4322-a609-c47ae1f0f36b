<?php
/**
 * اختبار اللوجو والتصميم الجديد
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اللوجو - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .logo-test {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 اختبار اللوجو والتصميم الجديد</h1>
        
        <!-- اختبار وجود اللوجو -->
        <div class="test-section">
            <h3>1. اختبار وجود ملف اللوجو</h3>
            <?php if (file_exists('assets/img/Logo.png')): ?>
                <div class="alert alert-success">
                    ✅ ملف اللوجو موجود: <code>assets/img/Logo.png</code>
                </div>
                
                <div class="logo-test">
                    <h5>معاينة اللوجو:</h5>
                    <img src="assets/img/Logo.png" alt="Logo" style="max-height: 100px; max-width: 200px;">
                    <br><small class="text-muted">الحجم الأصلي</small>
                </div>
                
                <div class="logo-test">
                    <h5>اللوجو في رأس الصفحة:</h5>
                    <img src="assets/img/Logo.png" alt="Logo" class="system-logo">
                    <br><small class="text-muted">حجم رأس الصفحة (60px)</small>
                </div>
                
                <div class="logo-test">
                    <h5>اللوجو في الكارت:</h5>
                    <img src="assets/img/Logo.png" alt="Logo" style="height: 15px;">
                    <br><small class="text-muted">حجم الكارت (15px)</small>
                </div>
                
            <?php else: ?>
                <div class="alert alert-danger">
                    ❌ ملف اللوجو غير موجود في: <code>assets/img/Logo.png</code>
                    <br><strong>الحل:</strong> تأكد من رفع ملف اللوجو في المسار الصحيح
                </div>
            <?php endif; ?>
        </div>
        
        <!-- اختبار ملفات CSS -->
        <div class="test-section">
            <h3>2. اختبار ملفات CSS</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>ملف CSS الرئيسي:</h5>
                    <?php if (file_exists('assets/style.css')): ?>
                        <div class="alert alert-success">✅ assets/style.css موجود</div>
                    <?php else: ?>
                        <div class="alert alert-danger">❌ assets/style.css غير موجود</div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6">
                    <h5>ملف CSS الطباعة:</h5>
                    <?php if (file_exists('assets/print.css')): ?>
                        <div class="alert alert-success">✅ assets/print.css موجود</div>
                    <?php else: ?>
                        <div class="alert alert-danger">❌ assets/print.css غير موجود</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار تصميم رأس الصفحة -->
        <div class="test-section">
            <h3>3. معاينة تصميم رأس الصفحة</h3>
            
            <div class="header" style="border-radius: 8px;">
                <div class="header-content">
                    <div class="logo-container">
                        <?php if (file_exists('assets/img/Logo.png')): ?>
                            <img src="assets/img/Logo.png" alt="MikroSys Logo" class="system-logo">
                        <?php else: ?>
                            <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.3); border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                                لوجو
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="header-text">
                        <h1><i class="fas fa-wifi"></i> نظام إدارة كروت الهوتسبوت</h1>
                        <p class="mb-0">MikroSys - إدارة شبكة المايكروتك</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-light btn-sm">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار تصميم الكارت -->
        <div class="test-section">
            <h3>4. معاينة تصميم الكارت</h3>
            
            <div style="background: #f0f0f0; padding: 20px; border-radius: 8px;">
                <div style="width: 70mm; height: 45mm; border: 1.5px solid #333; border-radius: 6px; padding: 2mm; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; position: relative; margin: 0 auto;">
                    <div style="display: flex; align-items: center; justify-content: space-between; border-bottom: 1px solid rgba(255,255,255,0.3); padding-bottom: 1mm; margin-bottom: 1.5mm;">
                        <?php if (file_exists('assets/img/Logo.png')): ?>
                            <img src="assets/img/Logo.png" alt="Logo" style="height: 15px; width: auto; max-width: 25px; object-fit: contain; opacity: 0.9;">
                        <?php endif; ?>
                        <div style="flex: 1; text-align: center;">
                            <div style="font-size: 10px; font-weight: bold; margin: 0;">كارت إنترنت</div>
                            <div style="font-size: 6px; margin: 0; opacity: 0.8;">MikroSys Hotspot</div>
                        </div>
                        <div style="font-size: 12px; opacity: 0.7;">📶</div>
                    </div>
                    
                    <div style="text-align: center; margin: 1mm 0;">
                        <div style="font-size: 6px; opacity: 0.8;">اسم المستخدم</div>
                        <div style="font-size: 11px; font-weight: bold; font-family: 'Courier New', monospace; background: rgba(255,255,255,0.2); padding: 1px 3px; border-radius: 2px;">TEST123</div>
                    </div>
                    
                    <div style="text-align: center; margin: 1mm 0;">
                        <div style="font-size: 6px; opacity: 0.8;">كلمة المرور</div>
                        <div style="font-size: 11px; font-weight: bold; font-family: 'Courier New', monospace; background: rgba(255,255,255,0.2); padding: 1px 3px; border-radius: 2px;">456789</div>
                    </div>
                    
                    <div style="text-align: center; border-top: 1px solid rgba(255,255,255,0.3); padding-top: 1mm; font-size: 5px; opacity: 0.8; line-height: 1.2;">
                        24 ساعة | صالح حتى: <?= date('d/m/Y', strtotime('+1 day')) ?>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">معاينة الكارت بالحجم الجديد (70×45مم)</small>
                </div>
            </div>
        </div>
        
        <!-- اختبار الروابط -->
        <div class="test-section">
            <h3>5. اختبار الروابط والصفحات</h3>
            
            <div class="row">
                <div class="col-md-4">
                    <h6>الصفحات الأساسية:</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" target="_blank">🏠 الصفحة الرئيسية</a></li>
                        <li><a href="settings.php" target="_blank">⚙️ الإعدادات</a></li>
                    </ul>
                </div>
                
                <div class="col-md-4">
                    <h6>صفحات الطباعة:</h6>
                    <ul class="list-unstyled">
                        <li><a href="print_cards.php" target="_blank">🖨️ طباعة عادية</a></li>
                        <li><a href="print_cards_advanced.php" target="_blank">🔧 طباعة متقدمة</a></li>
                    </ul>
                </div>
                
                <div class="col-md-4">
                    <h6>ملفات التوثيق:</h6>
                    <ul class="list-unstyled">
                        <li><a href="DESIGN_GUIDE.md" target="_blank">📖 دليل التصميم</a></li>
                        <li><a href="README.md" target="_blank">📋 دليل النظام</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- خلاصة الاختبار -->
        <div class="test-section">
            <h3>6. خلاصة الاختبار</h3>
            
            <?php
            $issues = [];
            
            if (!file_exists('assets/img/Logo.png')) {
                $issues[] = "ملف اللوجو غير موجود";
            }
            
            if (!file_exists('assets/style.css')) {
                $issues[] = "ملف CSS الرئيسي غير موجود";
            }
            
            if (!file_exists('assets/print.css')) {
                $issues[] = "ملف CSS الطباعة غير موجود";
            }
            
            if (empty($issues)): ?>
                <div class="alert alert-success">
                    <h5>🎉 جميع الاختبارات نجحت!</h5>
                    <p>التصميم الجديد جاهز للاستخدام. يمكنك الآن:</p>
                    <ul>
                        <li>استخدام النظام مع اللوجو الجديد</li>
                        <li>طباعة الكروت بالتصميم المحسن</li>
                        <li>الاستفادة من خيارات الطباعة المتقدمة</li>
                    </ul>
                    <div class="text-center mt-3">
                        <a href="index.php" class="btn btn-success">🚀 انتقل للنظام</a>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <h5>⚠️ توجد بعض المشاكل:</h5>
                    <ul>
                        <?php foreach ($issues as $issue): ?>
                            <li><?= $issue ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <p><strong>الحل:</strong> تأكد من رفع جميع الملفات في المسارات الصحيحة.</p>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">تم إنشاء هذا التقرير في: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    </div>
</body>
</html>
