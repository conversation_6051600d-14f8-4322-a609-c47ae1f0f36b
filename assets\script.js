/**
 * ملف JavaScript لنظام MikroSys
 * يحتوي على جميع التفاعلات والوظائف الديناميكية
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // إضافة تأثيرات الحركة
    addAnimations();
    
    // تهيئة الأزرار
    initializeButtons();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 1000);
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
}

/**
 * إضافة تأثيرات الحركة
 */
function addAnimations() {
    // إضافة تأثير fade-in للعناصر
    const elements = document.querySelectorAll('.card, .alert, .table');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.5s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * تهيئة الأزرار
 */
function initializeButtons() {
    // زر حذف الكارت
    const deleteButtons = document.querySelectorAll('.btn-delete-card');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const cardId = this.dataset.cardId;
            const cardUsername = this.dataset.cardUsername;
            
            showConfirmDialog(
                'حذف الكارت',
                `هل أنت متأكد من حذف الكارت "${cardUsername}"؟`,
                () => deleteCard(cardId)
            );
        });
    });
    
    // زر طباعة الكروت
    const printButton = document.getElementById('print-cards-btn');
    if (printButton) {
        printButton.addEventListener('click', function() {
            printCards();
        });
    }
    
    // زر تنظيف الكروت المنتهية
    const cleanupButton = document.getElementById('cleanup-btn');
    if (cleanupButton) {
        cleanupButton.addEventListener('click', function() {
            cleanupExpiredCards();
        });
    }
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // نموذج إنشاء الكروت
    const generateForm = document.getElementById('generate-cards-form');
    if (generateForm) {
        generateForm.addEventListener('submit', function(e) {
            const count = document.querySelector('input[name="count"]').value;
            
            if (count > 100) {
                e.preventDefault();
                showAlert('تحذير', 'لا يمكن إنشاء أكثر من 100 كارت في المرة الواحدة', 'warning');
                return false;
            }
            
            // إظهار مؤشر التحميل
            showLoadingIndicator('جاري إنشاء الكروت...');
        });
    }
    
    // التحقق من صحة البيانات في الوقت الفعلي
    const inputs = document.querySelectorAll('input[type="number"]');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            validateNumberInput(this);
        });
    });
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة فلترة للجداول
    addTableFilter();
    
    // إضافة ترقيم الصفحات
    addPagination();
    
    // تحديث حالة الكروت
    updateCardStatuses();
}

/**
 * حذف كارت
 */
function deleteCard(cardId) {
    showLoadingIndicator('جاري حذف الكارت...');
    
    fetch(`delete_card.php?id=${cardId}`, {
        method: 'GET'
    })
    .then(response => {
        if (response.ok) {
            // إعادة تحميل الصفحة
            window.location.reload();
        } else {
            throw new Error('فشل في حذف الكارت');
        }
    })
    .catch(error => {
        hideLoadingIndicator();
        showAlert('خطأ', error.message, 'error');
    });
}

/**
 * طباعة الكروت
 */
function printCards() {
    const printWindow = window.open('print_cards.php', '_blank', 'width=800,height=600');
    
    if (!printWindow) {
        showAlert('خطأ', 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.', 'error');
    }
}

/**
 * تنظيف الكروت المنتهية
 */
function cleanupExpiredCards() {
    showConfirmDialog(
        'تنظيف الكروت المنتهية',
        'هل تريد حذف جميع الكروت المنتهية الصلاحية من النظام؟',
        () => {
            showLoadingIndicator('جاري تنظيف الكروت...');
            
            fetch('cleanup.php?ajax=1', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingIndicator();
                showAlert('نجح', data.message, 'success');
                setTimeout(() => window.location.reload(), 2000);
            })
            .catch(error => {
                hideLoadingIndicator();
                showAlert('خطأ', 'فشل في تنظيف الكروت', 'error');
            });
        }
    );
}

/**
 * تحديث الإحصائيات
 */
function updateStats() {
    fetch('api/get_stats.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatsDisplay(data.stats);
        }
    })
    .catch(error => {
        console.log('فشل في تحديث الإحصائيات:', error);
    });
}

/**
 * تحديث عرض الإحصائيات
 */
function updateStatsDisplay(stats) {
    const elements = {
        'total-cards': stats.total_cards,
        'active-cards': stats.active_cards,
        'used-cards': stats.used_cards,
        'expired-cards': stats.expired_cards
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            animateNumber(element, elements[id]);
        }
    });
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, targetNumber) {
    const currentNumber = parseInt(element.textContent) || 0;
    const increment = (targetNumber - currentNumber) / 20;
    
    let current = currentNumber;
    const timer = setInterval(() => {
        current += increment;
        element.textContent = Math.round(current);
        
        if (Math.abs(current - targetNumber) < 1) {
            element.textContent = targetNumber;
            clearInterval(timer);
        }
    }, 50);
}

/**
 * إظهار مؤشر التحميل
 */
function showLoadingIndicator(message = 'جاري التحميل...') {
    const loader = document.createElement('div');
    loader.id = 'loading-indicator';
    loader.innerHTML = `
        <div class="loading-overlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>${message}</p>
            </div>
        </div>
    `;
    
    // إضافة CSS للمؤشر
    const style = document.createElement('style');
    style.textContent = `
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(loader);
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoadingIndicator() {
    const loader = document.getElementById('loading-indicator');
    if (loader) {
        loader.remove();
    }
}

/**
 * إظهار رسالة تأكيد
 */
function showConfirmDialog(title, message, onConfirm) {
    if (confirm(`${title}\n\n${message}`)) {
        onConfirm();
    }
}

/**
 * إظهار تنبيه
 */
function showAlert(title, message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show`;
    alert.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alert, container.firstChild);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

/**
 * التحقق من صحة إدخال الأرقام
 */
function validateNumberInput(input) {
    const value = parseInt(input.value);
    const min = parseInt(input.min) || 0;
    const max = parseInt(input.max) || Infinity;
    
    if (value < min) {
        input.value = min;
    } else if (value > max) {
        input.value = max;
    }
}

/**
 * إضافة فلترة للجداول
 */
function addTableFilter() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        const filterInput = document.createElement('input');
        filterInput.type = 'text';
        filterInput.className = 'form-control mb-3';
        filterInput.placeholder = 'البحث في الجدول...';
        
        filterInput.addEventListener('input', function() {
            filterTable(table, this.value);
        });
        
        table.parentNode.insertBefore(filterInput, table);
    });
}

/**
 * فلترة الجدول
 */
function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const shouldShow = text.includes(searchTerm.toLowerCase());
        row.style.display = shouldShow ? '' : 'none';
    });
}

/**
 * إضافة ترقيم الصفحات
 */
function addPagination() {
    // يمكن تطوير هذه الوظيفة لاحقاً
}

/**
 * تحديث حالة الكروت
 */
function updateCardStatuses() {
    const statusElements = document.querySelectorAll('[data-expires-at]');
    
    statusElements.forEach(element => {
        const expiresAt = new Date(element.dataset.expiresAt);
        const now = new Date();
        
        if (expiresAt < now && element.textContent === 'نشط') {
            element.textContent = 'منتهي';
            element.className = 'status-expired';
        }
    });
}

/**
 * تحديث الوقت
 */
function updateTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA');
    
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

/**
 * نسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('نجح', 'تم نسخ النص إلى الحافظة', 'success');
    }).catch(() => {
        showAlert('خطأ', 'فشل في نسخ النص', 'error');
    });
}

/**
 * تصدير البيانات إلى CSV
 */
function exportToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    let csv = '';
    const rows = table.querySelectorAll('tr');
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = Array.from(cols).map(col => 
            `"${col.textContent.replace(/"/g, '""')}"`
        ).join(',');
        csv += rowData + '\n';
    });
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}
