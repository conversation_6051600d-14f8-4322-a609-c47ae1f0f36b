/* نمط CSS مخصص لنظام MikroSys */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* تحسينات عامة */
body {
    font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* الحاوي الرئيسي */
.main-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin: 20px auto;
    max-width: 1200px;
    overflow: hidden;
}

/* رأس الصفحة */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.logo-container {
    flex-shrink: 0;
}

.system-logo {
    height: 60px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
    transition: transform 0.3s ease;
}

.system-logo:hover {
    transform: scale(1.05);
}

.header-text {
    flex: 1;
    text-align: center;
    margin: 0 20px;
}

.header-text h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
}

.header-text p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.header-actions {
    flex-shrink: 0;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 30s infinite linear;
    z-index: 1;
}

.header > * {
    position: relative;
    z-index: 2;
}

@keyframes float {
    0% { transform: translateX(-100px) translateY(-100px) rotate(0deg); }
    100% { transform: translateX(100px) translateY(100px) rotate(360deg); }
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 15px 20px;
    font-weight: bold;
}

/* الأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-generate {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    color: white;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
}

.btn-generate:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #17a085 100%);
}

/* حالات الكروت */
.status-active {
    color: var(--success-color);
    font-weight: bold;
}

.status-used {
    color: var(--danger-color);
    font-weight: bold;
}

.status-expired {
    color: var(--warning-color);
    font-weight: bold;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--dark-color) 0%, #495057 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: scale(1.01);
}

/* النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stats-card p {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
}

/* تحسينات للطباعة */
@media print {
    body {
        background: white !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    .main-container {
        box-shadow: none !important;
        margin: 0 !important;
        max-width: none !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-container {
        margin: 10px;
        border-radius: 10px;
    }

    .header {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-text h1 {
        font-size: 1.4rem;
    }

    .header-text p {
        font-size: 0.9rem;
    }

    .system-logo {
        height: 50px;
    }

    .btn {
        padding: 8px 20px;
        font-size: 14px;
    }

    .stats-card h3 {
        font-size: 2rem;
    }

    .table-responsive {
        font-size: 14px;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* تحسينات للنصوص العربية */
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* أيقونات مخصصة */
.icon-wifi::before {
    content: "📶";
    margin-left: 8px;
}

.icon-card::before {
    content: "🎫";
    margin-left: 8px;
}

.icon-settings::before {
    content: "⚙️";
    margin-left: 8px;
}

.icon-print::before {
    content: "🖨️";
    margin-left: 8px;
}

/* تحسينات للوصولية */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للحالة المظلمة */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .table {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .form-control,
    .form-select {
        background-color: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }
}
