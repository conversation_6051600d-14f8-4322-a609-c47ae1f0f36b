<?php
/**
 * اختبار بسيط للنظام
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار بسيط لنظام MikroSys</h1>";

// اختبار 1: PHP
echo "<h2>1. اختبار PHP</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";

// اختبار 2: الملفات
echo "<h2>2. اختبار الملفات</h2>";
$files = ['config.php', 'functions.php', 'mikrotik_api.php'];
foreach ($files as $file) {
    echo "ملف $file: ";
    if (file_exists($file)) {
        echo "✅ موجود<br>";
    } else {
        echo "❌ غير موجود<br>";
    }
}

// اختبار 3: قاعدة البيانات
echo "<h2>3. اختبار قاعدة البيانات</h2>";

$host = 'localhost';
$user = 'mikrosys';
$pass = 'mikrosys@2025';
$db = 'mikrosys';

try {
    $conn = new mysqli($host, $user, $pass, $db);
    
    if ($conn->connect_error) {
        echo "❌ فشل الاتصال: " . $conn->connect_error . "<br>";
        
        // محاولة الاتصال بدون قاعدة البيانات
        echo "محاولة الاتصال بالخادم فقط...<br>";
        $test_conn = new mysqli($host, $user, $pass);
        
        if ($test_conn->connect_error) {
            echo "❌ فشل الاتصال بالخادم: " . $test_conn->connect_error . "<br>";
            echo "<strong>الحل:</strong> تحقق من تشغيل MySQL وصحة بيانات المستخدم<br>";
        } else {
            echo "✅ الاتصال بالخادم نجح<br>";
            echo "❌ قاعدة البيانات '$db' غير موجودة<br>";
            
            // إنشاء قاعدة البيانات
            if ($test_conn->query("CREATE DATABASE IF NOT EXISTS `$db` CHARACTER SET utf8 COLLATE utf8_general_ci")) {
                echo "✅ تم إنشاء قاعدة البيانات<br>";
            } else {
                echo "❌ فشل إنشاء قاعدة البيانات: " . $test_conn->error . "<br>";
            }
            $test_conn->close();
        }
    } else {
        echo "✅ الاتصال بقاعدة البيانات نجح<br>";
        echo "إصدار MySQL: " . $conn->server_info . "<br>";
        
        // اختبار الجداول
        $tables = ['hotspot_cards', 'hotspot_profiles', 'system_settings'];
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "جدول $table: ✅ موجود<br>";
            } else {
                echo "جدول $table: ❌ غير موجود<br>";
            }
        }
        
        $conn->close();
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

// اختبار 4: تضمين الملفات
echo "<h2>4. اختبار تضمين الملفات</h2>";

try {
    if (file_exists('config.php')) {
        require_once 'config.php';
        echo "✅ تم تحميل config.php<br>";
    } else {
        echo "❌ ملف config.php غير موجود<br>";
    }
    
    if (file_exists('functions.php')) {
        require_once 'functions.php';
        echo "✅ تم تحميل functions.php<br>";
    } else {
        echo "❌ ملف functions.php غير موجود<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تحميل الملفات: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li>إذا كانت جميع الاختبارات ناجحة، انتقل إلى <a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li>إذا كانت هناك مشاكل، راجع ملف <a href='QUICK_FIX.md'>QUICK_FIX.md</a></li>";
echo "<li>لإعداد قاعدة البيانات يدوياً، استخدم ملف <code>setup_database.sql</code></li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>تم في: " . date('Y-m-d H:i:s') . "</small></p>";
?>
