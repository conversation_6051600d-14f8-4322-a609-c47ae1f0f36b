<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// التحقق من وجود كروت للطباعة
if (!isset($_SESSION['generated_cards']) || empty($_SESSION['generated_cards'])) {
    echo "<script>alert('لا توجد كروت للطباعة'); window.close();</script>";
    exit;
}

$cards = $_SESSION['generated_cards'];

// خيارات الطباعة
$print_style = $_GET['style'] ?? 'standard'; // standard, compact, mini, eco
$cards_per_row = intval($_GET['per_row'] ?? 4);
$show_logo = isset($_GET['logo']) ? $_GET['logo'] === '1' : true;
$color_mode = $_GET['color'] ?? 'color'; // color, bw, eco
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة كروت الهوتسبوت - MikroSys</title>
    <link href="assets/print.css" rel="stylesheet">
    <style>
        /* أنماط ديناميكية حسب الخيارات */
        .cards-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 2mm;
            margin: 0;
            padding: 5mm;
        }
        
        <?php if ($print_style === 'compact'): ?>
        .card {
            width: 65mm !important;
            height: 40mm !important;
            font-size: 8px !important;
            padding: 1.5mm !important;
        }
        .card-logo { height: 12px !important; }
        .credential-value { font-size: 9px !important; }
        <?php elseif ($print_style === 'mini'): ?>
        .card {
            width: 50mm !important;
            height: 32mm !important;
            font-size: 6px !important;
            padding: 1mm !important;
        }
        .card-logo { height: 8px !important; }
        .credential-value { font-size: 7px !important; }
        .card-footer { font-size: 3px !important; }
        <?php endif; ?>
        
        <?php if ($color_mode === 'bw'): ?>
        .card {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }
        .credential-value {
            background: #f0f0f0 !important;
            color: black !important;
        }
        <?php elseif ($color_mode === 'eco'): ?>
        .card {
            background: white !important;
            color: black !important;
            border: 1px solid black !important;
        }
        .card::before { display: none !important; }
        .credential-value {
            background: transparent !important;
            border: 1px dashed black !important;
            color: black !important;
        }
        <?php endif; ?>
        
        .card {
            width: calc(<?= 100 / $cards_per_row ?>% - 4mm);
            margin: 1mm;
        }
        
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; background: white; }
            .cards-container { padding: 2mm; }
        }
    </style>
</head>
<body>
    <!-- أدوات التحكم في الطباعة -->
    <div class="no-print" style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <h3>خيارات الطباعة المتقدمة</h3>
            
            <div style="display: flex; gap: 20px; flex-wrap: wrap; margin: 15px 0;">
                <div>
                    <label>حجم الكارت:</label>
                    <select id="style" onchange="updatePrint()">
                        <option value="standard" <?= $print_style === 'standard' ? 'selected' : '' ?>>عادي (70×45مم)</option>
                        <option value="compact" <?= $print_style === 'compact' ? 'selected' : '' ?>>مضغوط (65×40مم)</option>
                        <option value="mini" <?= $print_style === 'mini' ? 'selected' : '' ?>>مصغر (50×32مم)</option>
                    </select>
                </div>
                
                <div>
                    <label>الكروت في الصف:</label>
                    <select id="per_row" onchange="updatePrint()">
                        <option value="3" <?= $cards_per_row === 3 ? 'selected' : '' ?>>3 كروت</option>
                        <option value="4" <?= $cards_per_row === 4 ? 'selected' : '' ?>>4 كروت</option>
                        <option value="5" <?= $cards_per_row === 5 ? 'selected' : '' ?>>5 كروت</option>
                        <option value="6" <?= $cards_per_row === 6 ? 'selected' : '' ?>>6 كروت</option>
                    </select>
                </div>
                
                <div>
                    <label>نمط الألوان:</label>
                    <select id="color" onchange="updatePrint()">
                        <option value="color" <?= $color_mode === 'color' ? 'selected' : '' ?>>ملون</option>
                        <option value="bw" <?= $color_mode === 'bw' ? 'selected' : '' ?>>أبيض وأسود</option>
                        <option value="eco" <?= $color_mode === 'eco' ? 'selected' : '' ?>>اقتصادي (توفير حبر)</option>
                    </select>
                </div>
                
                <div>
                    <label>
                        <input type="checkbox" id="logo" <?= $show_logo ? 'checked' : '' ?> onchange="updatePrint()">
                        إظهار اللوجو
                    </label>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button onclick="window.print()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 5px;">
                    🖨️ طباعة
                </button>
                <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 5px;">
                    ❌ إغلاق
                </button>
            </div>
            
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                <strong>معلومات:</strong> 
                عدد الكروت: <?= count($cards) ?> | 
                الكروت في الصفحة: <?= $cards_per_row * 6 ?> | 
                عدد الصفحات المتوقع: <?= ceil(count($cards) / ($cards_per_row * 6)) ?>
            </div>
        </div>
    </div>
    
    <!-- الكروت -->
    <div class="cards-container">
        <?php foreach ($cards as $index => $card): ?>
            <div class="card">
                <div class="card-content">
                    <div class="card-header">
                        <?php if ($show_logo): ?>
                            <img src="assets/img/Logo.png" alt="Logo" class="card-logo">
                        <?php endif; ?>
                        <div style="flex: 1; text-align: center;">
                            <div class="card-title">كارت إنترنت</div>
                            <div class="card-subtitle">MikroSys Hotspot</div>
                        </div>
                        <div class="wifi-icon">📶</div>
                    </div>
                    
                    <div class="card-body">
                        <div class="credential">
                            <div class="credential-label">اسم المستخدم</div>
                            <div class="credential-value"><?= htmlspecialchars($card['username']) ?></div>
                        </div>
                        
                        <div class="credential">
                            <div class="credential-label">كلمة المرور</div>
                            <div class="credential-value"><?= htmlspecialchars($card['password']) ?></div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <?= htmlspecialchars($card['profile']) ?> | صالح حتى: <?= date('d/m/Y', strtotime($card['expires_at'])) ?>
                    </div>
                </div>
            </div>
            
            <?php if (($index + 1) % ($cards_per_row * 6) == 0 && $index + 1 < count($cards)): ?>
                <div style="page-break-after: always; width: 100%;"></div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
    
    <!-- فوتر الصفحة -->
    <div style="margin-top: 20px; text-align: center; font-size: 10px; color: #666; page-break-inside: avoid;" class="no-print-footer">
        <div style="display: flex; justify-content: center; align-items: center; gap: 10px; margin-bottom: 10px;">
            <?php if ($show_logo): ?>
                <img src="assets/img/Logo.png" alt="Logo" style="height: 25px; opacity: 0.7;">
            <?php endif; ?>
            <strong>MikroSys - نظام إدارة الهوتسبوت</strong>
        </div>
        <p style="margin: 5px 0;">طريقة الاستخدام: اتصل بالواي فاي → افتح المتصفح → ادخل البيانات</p>
        <p style="margin: 5px 0; font-size: 8px;">للدعم الفني: تواصل مع الإدارة | تاريخ الطباعة: <?= date('Y-m-d H:i') ?></p>
    </div>

    <script>
        function updatePrint() {
            const style = document.getElementById('style').value;
            const perRow = document.getElementById('per_row').value;
            const color = document.getElementById('color').value;
            const logo = document.getElementById('logo').checked ? '1' : '0';
            
            const url = new URL(window.location);
            url.searchParams.set('style', style);
            url.searchParams.set('per_row', perRow);
            url.searchParams.set('color', color);
            url.searchParams.set('logo', logo);
            
            window.location.href = url.toString();
        }
        
        // طباعة تلقائية عند الحاجة
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            setTimeout(() => {
                window.print();
            }, 1000);
        }
    </script>
</body>
</html>
