<?php
session_start();

// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

// التحقق من الاتصال بقاعدة البيانات
if (!$conn) {
    die("فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error());
}

// معالجة إنشاء كروت جديدة (مبسط)
if ($_POST['action'] == 'generate_cards' && isset($_POST['count'])) {
    $count = intval($_POST['count']);
    $profile = $_POST['profile'] ?? '24hours';
    
    // حساب مدة الصلاحية من الأيام والساعات والدقائق
    $validity_days = intval($_POST['validity_days'] ?? 1);
    $validity_hours = intval($_POST['validity_hours'] ?? 0);
    $validity_minutes = intval($_POST['validity_minutes'] ?? 0);
    
    // تحويل إلى ساعات
    $total_hours = ($validity_days * 24) + $validity_hours + ($validity_minutes / 60);
    
    $generated_cards = [];
    
    // إنشاء الكروت بدون MikroTik (للاختبار)
    for ($i = 0; $i < $count; $i++) {
        $username = 'USER' . rand(10000, 99999);
        $password = rand(100000, 999999);
        
        // حفظ في قاعدة البيانات
        $expires_at = date('Y-m-d H:i:s', strtotime("+$total_hours hours"));
        
        $stmt = $conn->prepare("INSERT INTO hotspot_cards (username, password, profile, expires_at) VALUES (?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param('ssss', $username, $password, $profile, $expires_at);
            if ($stmt->execute()) {
                $generated_cards[] = [
                    'username' => $username,
                    'password' => $password,
                    'profile' => $profile,
                    'expires_at' => $expires_at
                ];
            }
            $stmt->close();
        }
    }
    
    if (!empty($generated_cards)) {
        $_SESSION['success'] = "تم إنشاء {$count} كارت بنجاح";
        $_SESSION['generated_cards'] = $generated_cards;
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء إنشاء الكروت";
    }
}

// جلب البروفايلات المتاحة
$profiles = [];
$result = $conn->query("SELECT * FROM hotspot_profiles ORDER BY name");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $profiles[] = $row;
    }
}

// جلب الكروت الحديثة
$recent_cards = [];
$result = $conn->query("SELECT * FROM hotspot_cards ORDER BY created_at DESC LIMIT 10");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $recent_cards[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MikroSys - نسخة مبسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .main-container { min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .header { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 20px 0; margin-bottom: 30px; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="text-white mb-0">
                                <i class="fas fa-wifi"></i> MikroSys - نسخة مبسطة
                            </h1>
                            <p class="text-white-50 mb-0">نظام إدارة كروت الهوتسبوت</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="test_error.php" class="btn btn-warning btn-sm me-2">
                                <i class="fas fa-bug"></i> اختبار الأخطاء
                            </a>
                            <a href="index.php" class="btn btn-light btn-sm">
                                <i class="fas fa-sync"></i> النسخة الكاملة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="container">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <div class="row">
                    <!-- نموذج إنشاء الكروت -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-plus-circle"></i> إنشاء كروت جديدة</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="generate_cards">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">عدد الكروت</label>
                                        <input type="number" name="count" class="form-control" value="5" min="1" max="100" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">البروفايل</label>
                                        <select name="profile" class="form-control" required>
                                            <?php foreach ($profiles as $profile): ?>
                                                <option value="<?= htmlspecialchars($profile['name']) ?>">
                                                    <?= htmlspecialchars($profile['display_name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">مدة الصلاحية</label>
                                        <div class="row">
                                            <div class="col-4">
                                                <label class="form-label small">الأيام</label>
                                                <input type="number" name="validity_days" class="form-control" value="1" min="0" max="365">
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label small">الساعات</label>
                                                <input type="number" name="validity_hours" class="form-control" value="0" min="0" max="23">
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label small">الدقائق</label>
                                                <input type="number" name="validity_minutes" class="form-control" value="0" min="0" max="59">
                                            </div>
                                        </div>
                                        <div class="form-text">
                                            <small id="validity-preview">المدة الإجمالية: 1 يوم</small>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-magic"></i> إنشاء الكروت
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الكروت المنشأة حديثاً -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-dark text-white">
                                <h5><i class="fas fa-list"></i> الكروت الحديثة</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_cards)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>اسم المستخدم</th>
                                                    <th>كلمة المرور</th>
                                                    <th>البروفايل</th>
                                                    <th>تاريخ الانتهاء</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_cards as $card): ?>
                                                    <tr>
                                                        <td><strong><?= htmlspecialchars($card['username']) ?></strong></td>
                                                        <td><code><?= htmlspecialchars($card['password']) ?></code></td>
                                                        <td><?= htmlspecialchars($card['profile']) ?></td>
                                                        <td><?= date('Y-m-d H:i', strtotime($card['expires_at'])) ?></td>
                                                        <td>
                                                            <?php
                                                            $status_class = 'success';
                                                            $status_text = 'نشط';
                                                            if ($card['status'] == 'used') {
                                                                $status_class = 'warning';
                                                                $status_text = 'مستخدم';
                                                            } elseif ($card['status'] == 'expired') {
                                                                $status_class = 'danger';
                                                                $status_text = 'منتهي';
                                                            }
                                                            ?>
                                                            <span class="badge bg-<?= $status_class ?>"><?= $status_text ?></span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>لا توجد كروت بعد. أنشئ كروت جديدة للبدء.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات النظام -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6><i class="fas fa-info-circle"></i> معلومات النظام</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h6>قاعدة البيانات:</h6>
                                        <?php if ($conn): ?>
                                            <span class="badge bg-success">متصل</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير متصل</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <h6>عدد البروفايلات:</h6>
                                        <span class="badge bg-primary"><?= count($profiles) ?></span>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <h6>عدد الكروت:</h6>
                                        <?php
                                        $total_cards = 0;
                                        $result = $conn->query("SELECT COUNT(*) as count FROM hotspot_cards");
                                        if ($result) {
                                            $row = $result->fetch_assoc();
                                            $total_cards = $row['count'];
                                        }
                                        ?>
                                        <span class="badge bg-secondary"><?= $total_cards ?></span>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <h6>الحالة:</h6>
                                        <span class="badge bg-success">يعمل</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // حساب المدة الإجمالية
        function updateValidityPreview() {
            const days = parseInt(document.querySelector('input[name="validity_days"]').value) || 0;
            const hours = parseInt(document.querySelector('input[name="validity_hours"]').value) || 0;
            const minutes = parseInt(document.querySelector('input[name="validity_minutes"]').value) || 0;
            
            let preview = '';
            const parts = [];
            
            if (days > 0) parts.push(days + ' يوم');
            if (hours > 0) parts.push(hours + ' ساعة');
            if (minutes > 0) parts.push(minutes + ' دقيقة');
            
            if (parts.length === 0) {
                preview = 'يرجى تحديد مدة صالحة';
            } else {
                preview = 'المدة الإجمالية: ' + parts.join(' و ');
            }
            
            document.getElementById('validity-preview').textContent = preview;
        }
        
        // تحديث المعاينة عند تحميل الصفحة وعند تغيير القيم
        document.addEventListener('DOMContentLoaded', function() {
            updateValidityPreview();
            
            ['validity_days', 'validity_hours', 'validity_minutes'].forEach(name => {
                const input = document.querySelector(`input[name="${name}"]`);
                if (input) {
                    input.addEventListener('input', updateValidityPreview);
                }
            });
        });
    </script>
</body>
</html>
