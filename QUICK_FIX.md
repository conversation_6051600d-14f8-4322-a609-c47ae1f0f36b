# حل سريع لمشكلة Internal Server Error

## المشكلة
تظهر رسالة "Internal Server Error" عند محاولة الوصول للنظام.

## السبب المحتمل
مشكلة في إعدادات قاعدة البيانات أو عدم وجود المستخدم المطلوب.

## الحل السريع

### الخطوة 1: اختبار النظام
افتح المتصفح وانتقل إلى:
```
http://localhost/mikrosys/test_system.php
```

### الخطوة 2: إعداد قاعدة البيانات

#### الطريقة الأولى: استخدام phpMyAdmin
1. افتح phpMyAdmin من XAMPP Control Panel
2. انقر على "SQL" في الأعلى
3. انسخ محتوى ملف `setup_database.sql` والصقه
4. انقر "Go" لتنفيذ الأوامر

#### الطريقة الثانية: استخدام سطر الأوامر
```bash
# افتح Command Prompt كمدير
cd C:\xampp\mysql\bin

# تسجيل الدخول إلى MySQL
mysql -u root -p

# تنفيذ ملف الإعداد
source C:\xampp\htdocs\mikrosys\setup_database.sql
```

#### الطريقة الثالثة: إعداد يدوي سريع
افتح phpMyAdmin وقم بتنفيذ هذه الأوامر واحداً تلو الآخر:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `mikrosys` CHARACTER SET utf8 COLLATE utf8_general_ci;

-- إنشاء المستخدم
CREATE USER IF NOT EXISTS 'mikrosys'@'localhost' IDENTIFIED BY 'mikrosys@2025';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON `mikrosys`.* TO 'mikrosys'@'localhost';
FLUSH PRIVILEGES;
```

### الخطوة 3: التحقق من الحل
1. انتقل إلى: `http://localhost/mikrosys/test_system.php`
2. تأكد من ظهور ✅ أمام جميع الاختبارات
3. انتقل إلى: `http://localhost/mikrosys/index.php`

## حلول بديلة

### إذا استمرت المشكلة - الحل البديل 1
عدّل ملف `config.php` واستخدم المستخدم الافتراضي:

```php
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'mikrosys');
```

### إذا استمرت المشكلة - الحل البديل 2
تأكد من تشغيل خدمات XAMPP:
1. افتح XAMPP Control Panel
2. تأكد من تشغيل Apache و MySQL
3. إذا كانت متوقفة، انقر "Start"

### إذا استمرت المشكلة - الحل البديل 3
تحقق من سجل الأخطاء:
1. افتح مجلد `C:\xampp\apache\logs\`
2. افتح ملف `error.log`
3. ابحث عن آخر الأخطاء

## اختبار سريع للاتصال

أنشئ ملف `test_db.php` بهذا المحتوى:

```php
<?php
$host = 'localhost';
$user = 'mikrosys';
$pass = 'mikrosys@2025';
$db = 'mikrosys';

try {
    $conn = new mysqli($host, $user, $pass, $db);
    if ($conn->connect_error) {
        die("فشل الاتصال: " . $conn->connect_error);
    }
    echo "الاتصال نجح! ✅";
    $conn->close();
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
```

## إعادة تثبيت كاملة (إذا فشل كل شيء)

1. احذف قاعدة البيانات `mikrosys` من phpMyAdmin
2. احذف المستخدم `mikrosys` إذا كان موجوداً
3. انتقل إلى: `http://localhost/mikrosys/install.php`
4. اتبع خطوات التثبيت

## جهات الاتصال للدعم

إذا استمرت المشكلة:
1. تحقق من ملف `logs/error.log`
2. شارك رسالة الخطأ الكاملة
3. تأكد من إصدار PHP (يجب أن يكون 7.4+)

## ملاحظات مهمة

- تأكد من تشغيل XAMPP كمدير (Run as Administrator)
- تأكد من عدم وجود برامج أخرى تستخدم المنفذ 80 أو 3306
- في حالة استخدام WAMP أو MAMP، قد تحتاج لتعديل الإعدادات

## اختبار نهائي

بعد تطبيق الحل:
```
✅ http://localhost/mikrosys/test_system.php
✅ http://localhost/mikrosys/index.php
```

إذا ظهرت الصفحات بنجاح، فقد تم حل المشكلة! 🎉
