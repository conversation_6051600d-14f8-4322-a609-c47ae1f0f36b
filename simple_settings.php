<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// معالجة حفظ الإعدادات
if ($_POST['action'] == 'save_settings') {
    $mikrotik_host = validateInput($_POST['mikrotik_host']);
    $mikrotik_user = validateInput($_POST['mikrotik_user']);
    $mikrotik_pass = $_POST['mikrotik_pass'];
    $mikrotik_port = validateInput($_POST['mikrotik_port'], 'int');
    
    // حفظ الإعدادات
    setSetting('mikrotik_host', $mikrotik_host);
    setSetting('mikrotik_user', $mikrotik_user);
    setSetting('mikrotik_pass', $mikrotik_pass);
    setSetting('mikrotik_port', $mikrotik_port);
    setSetting('connection_type', 'ip'); // فقط IP للبساطة
    
    $_SESSION['success'] = "تم حفظ الإعدادات بنجاح";
}

// اختبار الاتصال
if ($_POST['action'] == 'test_connection') {
    if (checkMikroTikConnection()) {
        $_SESSION['success'] = "تم الاتصال بسيرفر MikroTik بنجاح!";
    } else {
        $_SESSION['error'] = "فشل الاتصال بسيرفر MikroTik. تحقق من الإعدادات.";
    }
}

// جلب الإعدادات الحالية
$current_settings = [
    'mikrotik_host' => getSetting('mikrotik_host', MIKROTIK_HOST),
    'mikrotik_user' => getSetting('mikrotik_user', MIKROTIK_USER),
    'mikrotik_pass' => getSetting('mikrotik_pass', MIKROTIK_PASS),
    'mikrotik_port' => getSetting('mikrotik_port', MIKROTIK_PORT)
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات MikroTik - مبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .main-container { min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .header { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 20px 0; margin-bottom: 30px; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="text-white mb-0">
                                <i class="fas fa-cog"></i> إعدادات MikroTik - مبسط
                            </h1>
                            <p class="text-white-50 mb-0">إعداد الاتصال بسيرفر MikroTik</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="debug_connection.php" class="btn btn-warning btn-sm me-2">
                                <i class="fas fa-bug"></i> تشخيص
                            </a>
                            <a href="index.php" class="btn btn-light btn-sm">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="container">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-server"></i> إعدادات الاتصال بـ MikroTik</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="save_settings">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">عنوان IP للسيرفر</label>
                                                <input type="text" name="mikrotik_host" class="form-control" 
                                                       value="<?= htmlspecialchars($current_settings['mikrotik_host']) ?>" 
                                                       placeholder="***********" required>
                                                <div class="form-text">عنوان IP الخاص بجهاز MikroTik</div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">منفذ API</label>
                                                <input type="number" name="mikrotik_port" class="form-control" 
                                                       value="<?= htmlspecialchars($current_settings['mikrotik_port']) ?>" 
                                                       placeholder="8728" required>
                                                <div class="form-text">منفذ API (افتراضي: 8728)</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم المستخدم</label>
                                                <input type="text" name="mikrotik_user" class="form-control" 
                                                       value="<?= htmlspecialchars($current_settings['mikrotik_user']) ?>" 
                                                       placeholder="admin" required>
                                                <div class="form-text">اسم المستخدم في MikroTik</div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">كلمة المرور</label>
                                                <input type="password" name="mikrotik_pass" class="form-control" 
                                                       value="<?= htmlspecialchars($current_settings['mikrotik_pass']) ?>" 
                                                       placeholder="كلمة المرور">
                                                <div class="form-text">كلمة مرور المستخدم</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-save"></i> حفظ الإعدادات
                                        </button>
                                    </div>
                                </form>
                                
                                <hr>
                                
                                <div class="text-center">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="test_connection">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-plug"></i> اختبار الاتصال
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الحالة -->
                <div class="row justify-content-center mt-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6><i class="fas fa-info-circle"></i> حالة النظام</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <h6>قاعدة البيانات:</h6>
                                        <?php if ($conn): ?>
                                            <span class="badge bg-success">متصل</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير متصل</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <h6>MikroTik API:</h6>
                                        <?php if (file_exists('mikrotik_api.php')): ?>
                                            <span class="badge bg-success">متوفر</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير متوفر</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <h6>اتصال MikroTik:</h6>
                                        <?php if (checkMikroTikConnection()): ?>
                                            <span class="badge bg-success">متصل</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير متصل</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <h6>الإعدادات الحالية:</h6>
                                        <small><?= $current_settings['mikrotik_host'] ?>:<?= $current_settings['mikrotik_port'] ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نصائح -->
                <div class="row justify-content-center mt-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h6><i class="fas fa-lightbulb"></i> نصائح لحل مشاكل الاتصال</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>في MikroTik:</h6>
                                        <ul class="list-unstyled">
                                            <li>• تفعيل API: <code>/ip service enable api</code></li>
                                            <li>• تعيين المنفذ: <code>/ip service set api port=8728</code></li>
                                            <li>• إنشاء مستخدم: <code>/user add name=admin password=123</code></li>
                                            <li>• فحص Firewall: <code>/ip firewall filter print</code></li>
                                        </ul>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6>في النظام:</h6>
                                        <ul class="list-unstyled">
                                            <li>• تحقق من عنوان IP الصحيح</li>
                                            <li>• تأكد من اسم المستخدم وكلمة المرور</li>
                                            <li>• اختبر الاتصال: <code>ping IP_ADDRESS</code></li>
                                            <li>• تحقق من منفذ API (افتراضي: 8728)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
