<?php
/**
 * اختبار نهائي شامل للنظام
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🎯 اختبار نهائي شامل للنظام</h1>";

require_once 'config.php';
require_once 'functions.php';

echo "<h2>1. اختبار قاعدة البيانات:</h2>";

if ($conn && !$conn->connect_error) {
    echo "<div style='color: green;'>✅ قاعدة البيانات متصلة بنجاح</div>";
    
    // فحص الجداول
    $tables = ['hotspot_cards', 'hotspot_profiles', 'system_settings'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<div style='color: green;'>✅ جدول $table موجود</div>";
        } else {
            echo "<div style='color: red;'>❌ جدول $table غير موجود</div>";
        }
    }
} else {
    echo "<div style='color: red;'>❌ مشكلة في قاعدة البيانات</div>";
}

echo "<h2>2. اختبار الاتصال بـ MikroTik:</h2>";

$mikrotik_status = checkMikroTikConnection();
if ($mikrotik_status) {
    echo "<div style='color: green; font-size: 18px;'>✅ الاتصال بـ MikroTik ناجح!</div>";
    
    // اختبار إنشاء مستخدم تجريبي
    echo "<h3>اختبار إنشاء مستخدم:</h3>";
    $test_username = 'TEST' . rand(1000, 9999);
    $test_password = rand(100000, 999999);
    
    if (createMikroTikUser($test_username, $test_password, 'default')) {
        echo "<div style='color: green;'>✅ تم إنشاء مستخدم تجريبي: $test_username</div>";
        
        // حذف المستخدم التجريبي
        if (deleteMikroTikUser($test_username)) {
            echo "<div style='color: green;'>✅ تم حذف المستخدم التجريبي</div>";
        } else {
            echo "<div style='color: orange;'>⚠️ تم إنشاء المستخدم لكن فشل في حذفه</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء المستخدم التجريبي</div>";
    }
    
} else {
    echo "<div style='color: red; font-size: 18px;'>❌ فشل الاتصال بـ MikroTik</div>";
}

echo "<h2>3. اختبار وظائف النظام:</h2>";

if ($mikrotik_status && $conn) {
    echo "<h3>أ) اختبار إنشاء كارت:</h3>";
    
    try {
        $cards = generateCards(1, 'default');
        if (!empty($cards)) {
            $card = $cards[0];
            echo "<div style='color: green;'>✅ تم إنشاء كارت تجريبي:</div>";
            echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>اسم المستخدم:</strong> " . $card['username'] . "<br>";
            echo "<strong>كلمة المرور:</strong> " . $card['password'] . "<br>";
            echo "<strong>البروفايل:</strong> " . $card['profile'];
            echo "</div>";
            
            // حذف الكارت التجريبي
            $conn->query("DELETE FROM hotspot_cards WHERE username = '" . $card['username'] . "'");
            deleteMikroTikUser($card['username']);
            echo "<div style='color: green;'>✅ تم حذف الكارت التجريبي</div>";
        } else {
            echo "<div style='color: red;'>❌ فشل في إنشاء الكارت</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في إنشاء الكارت: " . $e->getMessage() . "</div>";
    }
    
    echo "<h3>ب) اختبار البروفايلات:</h3>";
    $profiles = getAvailableProfiles();
    if (!empty($profiles)) {
        echo "<div style='color: green;'>✅ تم العثور على " . count($profiles) . " بروفايل:</div>";
        foreach ($profiles as $profile) {
            echo "<div>- " . $profile['display_name'] . " (" . $profile['name'] . ")</div>";
        }
    } else {
        echo "<div style='color: orange;'>⚠️ لا توجد بروفايلات محفوظة</div>";
    }
    
} else {
    echo "<div style='color: orange;'>⚠️ تم تخطي اختبار الوظائف بسبب مشاكل في الاتصال</div>";
}

echo "<h2>4. ملخص الحالة:</h2>";

$db_status = ($conn && !$conn->connect_error);
$overall_status = $db_status && $mikrotik_status;

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
echo "<tr style='background: #f8f9fa;'><th>المكون</th><th>الحالة</th></tr>";
echo "<tr><td>قاعدة البيانات</td><td>" . ($db_status ? "<span style='color: green;'>✅ يعمل</span>" : "<span style='color: red;'>❌ لا يعمل</span>") . "</td></tr>";
echo "<tr><td>MikroTik API</td><td>" . ($mikrotik_status ? "<span style='color: green;'>✅ يعمل</span>" : "<span style='color: red;'>❌ لا يعمل</span>") . "</td></tr>";
echo "<tr><td>النظام العام</td><td>" . ($overall_status ? "<span style='color: green;'>✅ جاهز للاستخدام</span>" : "<span style='color: red;'>❌ يحتاج إصلاح</span>") . "</td></tr>";
echo "</table>";

if ($overall_status) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border: 1px solid #c3e6cb; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h2 style='margin: 0;'>🎉 النظام يعمل بشكل مثالي!</h2>";
    echo "<p style='margin: 10px 0;'>يمكنك الآن استخدام جميع ميزات النظام بشكل طبيعي.</p>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>الذهاب إلى النظام الرئيسي</a>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h2 style='margin: 0;'>⚠️ النظام يحتاج إصلاح</h2>";
    echo "<p style='margin: 10px 0;'>يرجى حل المشاكل المذكورة أعلاه قبل استخدام النظام.</p>";
    
    if (!$db_status) {
        echo "<a href='fix_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إصلاح قاعدة البيانات</a>";
    }
    
    if (!$mikrotik_status) {
        echo "<a href='advanced_debug.php' style='background: #fd7e14; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص MikroTik</a>";
    }
    echo "</div>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<h3>روابط مفيدة:</h3>";
echo "<a href='test_mikrotik_connection.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار MikroTik</a>";
echo "<a href='detailed_debug.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص مفصل</a>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>النظام الرئيسي</a>";
echo "</div>";
?>
