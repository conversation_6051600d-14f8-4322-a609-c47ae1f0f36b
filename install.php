<?php
/**
 * ملف التثبيت التلقائي لنظام MikroSys
 * يقوم بإعداد قاعدة البيانات والإعدادات الأساسية
 */

// التحقق من وجود ملف الإعدادات
if (file_exists('config.php')) {
    require_once 'config.php';
    
    // إذا كان النظام مثبت بالفعل، توجيه للصفحة الرئيسية
    if ($conn && mysqli_num_rows(mysqli_query($conn, "SHOW TABLES LIKE 'hotspot_cards'")) > 0) {
        header('Location: index.php');
        exit;
    }
}

$step = isset($_GET['step']) ? intval($_GET['step']) : 1;
$errors = [];
$success_messages = [];

// معالجة خطوات التثبيت
if ($_POST) {
    switch ($step) {
        case 1:
            // التحقق من متطلبات النظام
            $requirements_met = checkSystemRequirements();
            if ($requirements_met) {
                header('Location: install.php?step=2');
                exit;
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $db_config = setupDatabase($_POST);
            if ($db_config['success']) {
                header('Location: install.php?step=3');
                exit;
            } else {
                $errors = $db_config['errors'];
            }
            break;
            
        case 3:
            // إعداد MikroTik
            $mikrotik_config = setupMikroTik($_POST);
            if ($mikrotik_config['success']) {
                header('Location: install.php?step=4');
                exit;
            } else {
                $errors = $mikrotik_config['errors'];
            }
            break;
            
        case 4:
            // إنهاء التثبيت
            finishInstallation();
            header('Location: index.php');
            exit;
            break;
    }
}

/**
 * التحقق من متطلبات النظام
 */
function checkSystemRequirements() {
    global $errors;
    
    // التحقق من إصدار PHP
    if (version_compare(PHP_VERSION, '7.4.0', '<')) {
        $errors[] = 'يتطلب النظام PHP 7.4 أو أحدث. الإصدار الحالي: ' . PHP_VERSION;
    }
    
    // التحقق من امتدادات PHP المطلوبة
    $required_extensions = ['mysqli', 'json', 'mbstring'];
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $errors[] = "امتداد PHP مطلوب غير مثبت: $ext";
        }
    }
    
    // التحقق من صلاحيات الكتابة
    if (!is_writable('.')) {
        $errors[] = 'لا توجد صلاحيات كتابة في المجلد الحالي';
    }
    
    return empty($errors);
}

/**
 * إعداد قاعدة البيانات
 */
function setupDatabase($data) {
    $host = $data['db_host'] ?? 'localhost';
    $user = $data['db_user'] ?? 'root';
    $pass = $data['db_pass'] ?? '';
    $name = $data['db_name'] ?? 'mikrosys_hotspot';
    
    $errors = [];
    
    try {
        // محاولة الاتصال
        $conn = new mysqli($host, $user, $pass);
        
        if ($conn->connect_error) {
            throw new Exception('فشل الاتصال بخادم قاعدة البيانات: ' . $conn->connect_error);
        }
        
        // إنشاء قاعدة البيانات
        $conn->query("CREATE DATABASE IF NOT EXISTS `$name` CHARACTER SET utf8 COLLATE utf8_general_ci");
        $conn->select_db($name);
        
        // إنشاء الجداول
        createTables($conn);
        
        // إنشاء ملف الإعدادات
        createConfigFile($host, $user, $pass, $name);
        
        $conn->close();
        
        return ['success' => true];
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
        return ['success' => false, 'errors' => $errors];
    }
}

/**
 * إنشاء الجداول
 */
function createTables($conn) {
    $tables = [
        "CREATE TABLE IF NOT EXISTS hotspot_cards (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(50) NOT NULL,
            profile VARCHAR(50) NOT NULL,
            status ENUM('active', 'used', 'expired') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            used_at TIMESTAMP NULL,
            mikrotik_id VARCHAR(50) NULL,
            notes TEXT NULL,
            INDEX idx_status (status),
            INDEX idx_created_at (created_at),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8",
        
        "CREATE TABLE IF NOT EXISTS hotspot_profiles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            display_name VARCHAR(100) NOT NULL,
            session_timeout INT DEFAULT 0,
            idle_timeout INT DEFAULT 0,
            rate_limit VARCHAR(50) DEFAULT '',
            shared_users INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8",
        
        "CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8"
    ];
    
    foreach ($tables as $sql) {
        if (!$conn->query($sql)) {
            throw new Exception('فشل في إنشاء الجداول: ' . $conn->error);
        }
    }
    
    // إدراج البروفايلات الافتراضية
    insertDefaultProfiles($conn);
}

/**
 * إدراج البروفايلات الافتراضية
 */
function insertDefaultProfiles($conn) {
    $profiles = [
        ['1hour', 'ساعة واحدة', 3600, 300, '1M/1M', 1],
        ['3hours', '3 ساعات', 10800, 300, '2M/2M', 1],
        ['6hours', '6 ساعات', 21600, 600, '3M/3M', 1],
        ['12hours', '12 ساعة', 43200, 600, '5M/5M', 1],
        ['24hours', '24 ساعة', 86400, 900, '10M/10M', 1],
        ['7days', 'أسبوع', 604800, 1800, '15M/15M', 2],
        ['30days', 'شهر', 2592000, 3600, '20M/20M', 3]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO hotspot_profiles (name, display_name, session_timeout, idle_timeout, rate_limit, shared_users) VALUES (?, ?, ?, ?, ?, ?)");
    
    foreach ($profiles as $profile) {
        $stmt->bind_param('ssiisi', $profile[0], $profile[1], $profile[2], $profile[3], $profile[4], $profile[5]);
        $stmt->execute();
    }
    
    $stmt->close();
}

/**
 * إنشاء ملف الإعدادات
 */
function createConfigFile($host, $user, $pass, $name) {
    $config_content = "<?php
// إعدادات قاعدة البيانات
define('DB_HOST', '$host');
define('DB_USER', '$user');
define('DB_PASS', '$pass');
define('DB_NAME', '$name');

// إعدادات MikroTik (سيتم تحديثها في الخطوة التالية)
define('MIKROTIK_HOST', '***********');
define('MIKROTIK_USER', 'admin');
define('MIKROTIK_PASS', '');
define('MIKROTIK_PORT', 8728);

// إعدادات النظام
define('SYSTEM_NAME', 'MikroSys Hotspot Manager');
define('CARDS_PER_PAGE', 50);
define('DEFAULT_PROFILE', '24hours');

// الاتصال بقاعدة البيانات
\$conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// تعيين الترميز
if (\$conn) {
    mysqli_set_charset(\$conn, \"utf8\");
}

// دالة لتسجيل الأخطاء
function logError(\$message) {
    \$log_file = 'logs/error.log';
    \$timestamp = date('Y-m-d H:i:s');
    \$log_message = \"[\$timestamp] \$message\" . PHP_EOL;
    
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents(\$log_file, \$log_message, FILE_APPEND | LOCK_EX);
}

// دالة للحصول على إعداد من قاعدة البيانات
function getSetting(\$key, \$default = '') {
    global \$conn;
    \$result = mysqli_query(\$conn, \"SELECT setting_value FROM system_settings WHERE setting_key = '\$key'\");
    if (\$result && mysqli_num_rows(\$result) > 0) {
        \$row = mysqli_fetch_assoc(\$result);
        return \$row['setting_value'];
    }
    return \$default;
}

// دالة لحفظ إعداد في قاعدة البيانات
function setSetting(\$key, \$value) {
    global \$conn;
    \$sql = \"INSERT INTO system_settings (setting_key, setting_value) VALUES ('\$key', '\$value') 
            ON DUPLICATE KEY UPDATE setting_value = '\$value'\";
    return mysqli_query(\$conn, \$sql);
}

// التحقق من حالة الاتصال بـ MikroTik
function checkMikroTikConnection() {
    try {
        require_once 'mikrotik_api.php';
        \$api = new RouterosAPI();
        \$api->debug = false;
        
        if (\$api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            \$api->disconnect();
            return true;
        }
        return false;
    } catch (Exception \$e) {
        logError(\"MikroTik connection error: \" . \$e->getMessage());
        return false;
    }
}
?>";
    
    if (!file_put_contents('config.php', $config_content)) {
        throw new Exception('فشل في إنشاء ملف الإعدادات');
    }
}

/**
 * إعداد MikroTik
 */
function setupMikroTik($data) {
    $host = $data['mikrotik_host'] ?? '***********';
    $user = $data['mikrotik_user'] ?? 'admin';
    $pass = $data['mikrotik_pass'] ?? '';
    $port = intval($data['mikrotik_port'] ?? 8728);
    
    // تحديث ملف الإعدادات
    $config_content = file_get_contents('config.php');
    $config_content = str_replace("define('MIKROTIK_HOST', '***********');", "define('MIKROTIK_HOST', '$host');", $config_content);
    $config_content = str_replace("define('MIKROTIK_USER', 'admin');", "define('MIKROTIK_USER', '$user');", $config_content);
    $config_content = str_replace("define('MIKROTIK_PASS', '');", "define('MIKROTIK_PASS', '$pass');", $config_content);
    $config_content = str_replace("define('MIKROTIK_PORT', 8728);", "define('MIKROTIK_PORT', $port);", $config_content);
    
    file_put_contents('config.php', $config_content);
    
    return ['success' => true];
}

/**
 * إنهاء التثبيت
 */
function finishInstallation() {
    // إنشاء مجلد اللوجات
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    // إنشاء ملف .htaccess للحماية
    $htaccess_content = "# حماية ملفات الإعدادات
<Files \"config.php\">
    Order allow,deny
    Deny from all
</Files>

<Files \"*.log\">
    Order allow,deny
    Deny from all
</Files>

# تفعيل ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين الكاش
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
</IfModule>";
    
    file_put_contents('.htaccess', $htaccess_content);
    
    // حذف ملف التثبيت (اختياري)
    // unlink(__FILE__);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1><i class="fas fa-cog"></i> تثبيت نظام MikroSys</h1>
            <p class="mb-0">نظام إدارة كروت الهوتسبوت</p>
        </div>
        
        <div class="step-indicator">
            <div class="step <?= $step >= 1 ? ($step == 1 ? 'active' : 'completed') : '' ?>">1</div>
            <div class="step <?= $step >= 2 ? ($step == 2 ? 'active' : 'completed') : '' ?>">2</div>
            <div class="step <?= $step >= 3 ? ($step == 3 ? 'active' : 'completed') : '' ?>">3</div>
            <div class="step <?= $step >= 4 ? ($step == 4 ? 'active' : 'completed') : '' ?>">4</div>
        </div>
        
        <div class="p-4">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h6>حدثت الأخطاء التالية:</h6>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success">
                    <?php foreach ($success_messages as $message): ?>
                        <div><?= htmlspecialchars($message) ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php
            switch ($step) {
                case 1:
                    include 'install_steps/step1.php';
                    break;
                case 2:
                    include 'install_steps/step2.php';
                    break;
                case 3:
                    include 'install_steps/step3.php';
                    break;
                case 4:
                    include 'install_steps/step4.php';
                    break;
            }
            ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
