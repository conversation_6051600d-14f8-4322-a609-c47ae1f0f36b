<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "بدء الاختبار...<br>";

try {
    echo "1. اختبار config.php...<br>";
    require_once 'config.php';
    echo "✅ config.php تم تحميله بنجاح<br>";
    
    echo "2. اختبار functions.php...<br>";
    require_once 'functions.php';
    echo "✅ functions.php تم تحميله بنجاح<br>";
    
    echo "3. اختبار الاتصال بقاعدة البيانات...<br>";
    if ($conn) {
        echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    } else {
        echo "❌ فشل الاتصال بقاعدة البيانات<br>";
    }
    
    echo "4. اختبار الدوال الأساسية...<br>";
    
    if (function_exists('generateUsername')) {
        echo "✅ دالة generateUsername موجودة<br>";
    } else {
        echo "❌ دالة generateUsername غير موجودة<br>";
    }
    
    if (function_exists('checkMikroTikConnection')) {
        echo "✅ دالة checkMikroTikConnection موجودة<br>";
    } else {
        echo "❌ دالة checkMikroTikConnection غير موجودة<br>";
    }
    
    if (function_exists('getMikroTikConnectionInfo')) {
        echo "✅ دالة getMikroTikConnectionInfo موجودة<br>";
    } else {
        echo "❌ دالة getMikroTikConnectionInfo غير موجودة<br>";
    }
    
    if (function_exists('findIpFromMac')) {
        echo "✅ دالة findIpFromMac موجودة<br>";
    } else {
        echo "❌ دالة findIpFromMac غير موجودة<br>";
    }
    
    echo "5. اختبار الثوابت...<br>";
    
    if (defined('MIKROTIK_HOST')) {
        echo "✅ MIKROTIK_HOST: " . MIKROTIK_HOST . "<br>";
    } else {
        echo "❌ MIKROTIK_HOST غير معرف<br>";
    }
    
    if (defined('MIKROTIK_CONNECTION_TYPE')) {
        echo "✅ MIKROTIK_CONNECTION_TYPE: " . MIKROTIK_CONNECTION_TYPE . "<br>";
    } else {
        echo "❌ MIKROTIK_CONNECTION_TYPE غير معرف<br>";
    }
    
    if (defined('MIKROTIK_MAC')) {
        echo "✅ MIKROTIK_MAC: " . (empty(MIKROTIK_MAC) ? '(فارغ)' : MIKROTIK_MAC) . "<br>";
    } else {
        echo "❌ MIKROTIK_MAC غير معرف<br>";
    }
    
    echo "6. اختبار دالة getMikroTikConnectionInfo...<br>";
    $connection_info = getMikroTikConnectionInfo();
    echo "✅ معلومات الاتصال: <pre>" . print_r($connection_info, true) . "</pre>";
    
    echo "<br>🎉 جميع الاختبارات نجحت!<br>";
    echo "<a href='index.php'>جرب الصفحة الرئيسية الآن</a>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "في الملف: " . $e->getFile() . "<br>";
    echo "في السطر: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ خطأ فادح: " . $e->getMessage() . "<br>";
    echo "في الملف: " . $e->getFile() . "<br>";
    echo "في السطر: " . $e->getLine() . "<br>";
}
?>
