<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// التحقق من وجود معرف الكارت
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "معرف الكارت غير صحيح";
    header('Location: index.php');
    exit;
}

$card_id = intval($_GET['id']);

// حذف الكارت
if (deleteCard($card_id)) {
    $_SESSION['success'] = "تم حذف الكارت بنجاح";
} else {
    $_SESSION['error'] = "حدث خطأ أثناء حذف الكارت";
}

// العودة للصفحة الرئيسية
header('Location: index.php');
exit;
?>
