<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// التحقق من وجود كروت للطباعة
if (!isset($_SESSION['generated_cards']) || empty($_SESSION['generated_cards'])) {
    echo "<script>alert('لا توجد كروت للطباعة'); window.close();</script>";
    exit;
}

$cards = $_SESSION['generated_cards'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة كروت الهوتسبوت</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
            .page-break { page-break-after: always; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .card {
            width: 70mm;
            height: 45mm;
            border: 1.5px solid #333;
            border-radius: 6px;
            margin: 2mm;
            padding: 2mm;
            display: inline-block;
            vertical-align: top;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
            page-break-inside: avoid;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateX(-100px) translateY(-100px); }
            100% { transform: translateX(100px) translateY(100px); }
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 1mm;
            margin-bottom: 1.5mm;
        }

        .card-logo {
            height: 15px;
            width: auto;
            max-width: 25px;
            object-fit: contain;
            opacity: 0.9;
        }

        .card-title {
            font-size: 10px;
            font-weight: bold;
            margin: 0;
            flex: 1;
            text-align: center;
        }

        .card-subtitle {
            font-size: 6px;
            margin: 0;
            opacity: 0.8;
            text-align: center;
        }
        
        .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .credential {
            margin: 1mm 0;
            text-align: center;
        }
        
        .credential-label {
            font-size: 6px;
            opacity: 0.8;
            margin-bottom: 0.5px;
        }

        .credential-value {
            font-size: 11px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            background: rgba(255,255,255,0.2);
            padding: 1px 3px;
            border-radius: 2px;
            letter-spacing: 0.5px;
        }

        .card-footer {
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 1mm;
            font-size: 5px;
            opacity: 0.8;
            line-height: 1.2;
        }
        
        .wifi-icon {
            font-size: 12px;
            opacity: 0.7;
        }
        
        .print-controls {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .cards-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 1mm;
        }

        .cards-row {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
        }
        
        .page-info {
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <h2>طباعة كروت الهوتسبوت</h2>
        <p>عدد الكروت: <strong><?= count($cards) ?></strong></p>
        <button class="btn btn-success" onclick="window.print()">
            🖨️ طباعة الكروت
        </button>
        <button class="btn" onclick="window.close()">
            ❌ إغلاق
        </button>
    </div>
    
    <div class="print-container">
        <div class="page-info no-print">
            <strong>كروت الهوتسبوت - <?= date('Y-m-d H:i') ?></strong>
        </div>
        
        <div class="cards-grid">
            <?php foreach ($cards as $index => $card): ?>
                <div class="card">
                    <div class="card-content">
                        <div class="card-header">
                            <img src="assets/img/Logo.png" alt="Logo" class="card-logo">
                            <div>
                                <div class="card-title">كارت إنترنت</div>
                                <div class="card-subtitle">MikroSys Hotspot</div>
                            </div>
                            <div class="wifi-icon">📶</div>
                        </div>
                        
                        <div class="card-body">
                            <div class="credential">
                                <div class="credential-label">اسم المستخدم</div>
                                <div class="credential-value"><?= htmlspecialchars($card['username']) ?></div>
                            </div>
                            
                            <div class="credential">
                                <div class="credential-label">كلمة المرور</div>
                                <div class="credential-value"><?= htmlspecialchars($card['password']) ?></div>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <div><?= htmlspecialchars($card['profile']) ?> | صالح حتى: <?= date('d/m/Y', strtotime($card['expires_at'])) ?></div>
                        </div>
                    </div>
                </div>
                
                <?php if (($index + 1) % 12 == 0 && $index + 1 < count($cards)): ?>
                    <div class="page-break"></div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <div style="margin-top: 20px; text-align: center; font-size: 10px; color: #666; page-break-inside: avoid;">
            <div style="display: flex; justify-content: center; align-items: center; gap: 10px; margin-bottom: 10px;">
                <img src="assets/img/Logo.png" alt="Logo" style="height: 25px; opacity: 0.7;">
                <strong>MikroSys - نظام إدارة الهوتسبوت</strong>
            </div>
            <p style="margin: 5px 0;"><strong>طريقة الاستخدام:</strong> اتصل بالواي فاي → افتح المتصفح → ادخل البيانات</p>
            <p style="margin: 5px 0; font-size: 8px;">للدعم الفني: تواصل مع الإدارة</p>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // };
        
        // إغلاق النافذة بعد الطباعة
        window.onafterprint = function() {
            // window.close();
        };
    </script>
</body>
</html>
