<h3><i class="fas fa-check-circle"></i> التحقق من متطلبات النظام</h3>
<p>سيتم التحقق من توفر جميع المتطلبات اللازمة لتشغيل النظام.</p>

<div class="requirements-check">
    <h5>متطلبات الخادم:</h5>
    <ul class="list-group">
        <li class="list-group-item d-flex justify-content-between align-items-center">
            PHP 7.4+
            <?php if (version_compare(PHP_VERSION, '7.4.0', '>=')): ?>
                <span class="badge bg-success"><i class="fas fa-check"></i> <?= PHP_VERSION ?></span>
            <?php else: ?>
                <span class="badge bg-danger"><i class="fas fa-times"></i> <?= PHP_VERSION ?></span>
            <?php endif; ?>
        </li>
        
        <li class="list-group-item d-flex justify-content-between align-items-center">
            امتداد MySQLi
            <?php if (extension_loaded('mysqli')): ?>
                <span class="badge bg-success"><i class="fas fa-check"></i> متوفر</span>
            <?php else: ?>
                <span class="badge bg-danger"><i class="fas fa-times"></i> غير متوفر</span>
            <?php endif; ?>
        </li>
        
        <li class="list-group-item d-flex justify-content-between align-items-center">
            امتداد JSON
            <?php if (extension_loaded('json')): ?>
                <span class="badge bg-success"><i class="fas fa-check"></i> متوفر</span>
            <?php else: ?>
                <span class="badge bg-danger"><i class="fas fa-times"></i> غير متوفر</span>
            <?php endif; ?>
        </li>
        
        <li class="list-group-item d-flex justify-content-between align-items-center">
            امتداد mbstring
            <?php if (extension_loaded('mbstring')): ?>
                <span class="badge bg-success"><i class="fas fa-check"></i> متوفر</span>
            <?php else: ?>
                <span class="badge bg-danger"><i class="fas fa-times"></i> غير متوفر</span>
            <?php endif; ?>
        </li>
        
        <li class="list-group-item d-flex justify-content-between align-items-center">
            صلاحيات الكتابة
            <?php if (is_writable('.')): ?>
                <span class="badge bg-success"><i class="fas fa-check"></i> متوفرة</span>
            <?php else: ?>
                <span class="badge bg-danger"><i class="fas fa-times"></i> غير متوفرة</span>
            <?php endif; ?>
        </li>
    </ul>
</div>

<div class="mt-4">
    <h5>معلومات الخادم:</h5>
    <div class="row">
        <div class="col-md-6">
            <small class="text-muted">نظام التشغيل:</small><br>
            <strong><?= PHP_OS ?></strong>
        </div>
        <div class="col-md-6">
            <small class="text-muted">خادم الويب:</small><br>
            <strong><?= $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف' ?></strong>
        </div>
    </div>
</div>

<form method="POST" class="mt-4">
    <div class="text-center">
        <?php if (checkSystemRequirements()): ?>
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-left"></i> متابعة للخطوة التالية
            </button>
        <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                يرجى حل المشاكل المذكورة أعلاه قبل المتابعة
            </div>
            <button type="button" class="btn btn-secondary" onclick="window.location.reload()">
                <i class="fas fa-redo"></i> إعادة فحص
            </button>
        <?php endif; ?>
    </div>
</form>
