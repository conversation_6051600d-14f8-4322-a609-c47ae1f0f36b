<?php
/**
 * فحص الأمان ومراقبة محاولات الاختراق
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🛡️ فحص الأمان - MikroTik</h1>";

require_once 'config.php';

echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🚨 تحذير أمني</h2>";
echo "<p>تم اكتشاف محاولة اختراق على راوتر MikroTik:</p>";
echo "<ul>";
echo "<li><strong>العنوان المهاجم:</strong> *************</li>";
echo "<li><strong>المستخدم المستهدف:</strong> UIpe</li>";
echo "<li><strong>نوع الهجوم:</strong> Brute Force عبر API</li>";
echo "</ul>";
echo "</div>";

if (file_exists('mikrotik_api.php')) {
    require_once 'mikrotik_api.php';
    
    try {
        $api = new RouterosAPI();
        $api->debug = false;
        
        if ($api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            echo "<div style='color: green;'>✅ متصل بـ MikroTik بنجاح</div>";
            
            echo "<h2>1. فحص المستخدمين:</h2>";
            try {
                $users = $api->comm('/user/print');
                if (!empty($users)) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                    echo "<tr style='background: #f8f9fa;'><th>الاسم</th><th>المجموعة</th><th>آخر دخول</th><th>الحالة</th></tr>";
                    
                    foreach ($users as $user) {
                        $name = $user['name'] ?? 'غير محدد';
                        $group = $user['group'] ?? 'غير محدد';
                        $lastLogin = $user['last-logged-in'] ?? 'لم يدخل';
                        $disabled = isset($user['disabled']) && $user['disabled'] === 'true' ? 'معطل' : 'مفعل';
                        
                        // تمييز المستخدمين المشبوهين
                        $suspicious = in_array($name, ['UIpe', 'admin', 'user', 'test', 'guest']);
                        $rowStyle = $suspicious ? "background: #f8d7da;" : "";
                        
                        echo "<tr style='$rowStyle'>";
                        echo "<td>$name</td>";
                        echo "<td>$group</td>";
                        echo "<td>$lastLogin</td>";
                        echo "<td>$disabled</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    // اقتراحات الأمان
                    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h3>🔧 اقتراحات الأمان:</h3>";
                    echo "<ul>";
                    echo "<li>احذف أي مستخدمين مشبوهين (مثل UIpe)</li>";
                    echo "<li>غير كلمات المرور للمستخدمين المهمين</li>";
                    echo "<li>تأكد من أن admin لديه كلمة مرور قوية</li>";
                    echo "</ul>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div style='color: red;'>خطأ في قراءة المستخدمين: " . $e->getMessage() . "</div>";
            }
            
            echo "<h2>2. فحص خدمات الشبكة:</h2>";
            try {
                $services = $api->comm('/ip/service/print');
                if (!empty($services)) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                    echo "<tr style='background: #f8f9fa;'><th>الخدمة</th><th>البورت</th><th>العنوان</th><th>الحالة</th><th>الأمان</th></tr>";
                    
                    foreach ($services as $service) {
                        $name = $service['name'] ?? '';
                        $port = $service['port'] ?? '';
                        $address = $service['address'] ?? 'الكل';
                        $disabled = isset($service['disabled']) && $service['disabled'] === 'true' ? 'معطل' : 'مفعل';
                        
                        // تقييم الأمان
                        $security = 'آمن';
                        $securityColor = 'green';
                        
                        if ($name === 'api' && $address === '') {
                            $security = 'خطر - مفتوح للإنترنت';
                            $securityColor = 'red';
                        } elseif (in_array($name, ['telnet', 'ftp']) && $disabled === 'مفعل') {
                            $security = 'تحذير - خدمة غير آمنة';
                            $securityColor = 'orange';
                        }
                        
                        echo "<tr>";
                        echo "<td>$name</td>";
                        echo "<td>$port</td>";
                        echo "<td>$address</td>";
                        echo "<td>$disabled</td>";
                        echo "<td style='color: $securityColor;'>$security</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } catch (Exception $e) {
                echo "<div style='color: red;'>خطأ في قراءة الخدمات: " . $e->getMessage() . "</div>";
            }
            
            echo "<h2>3. فحص جدار الحماية:</h2>";
            try {
                $firewall = $api->comm('/ip/firewall/filter/print');
                $firewallCount = count($firewall);
                
                if ($firewallCount > 0) {
                    echo "<div style='color: green;'>✅ يوجد $firewallCount قاعدة في جدار الحماية</div>";
                } else {
                    echo "<div style='color: red;'>❌ لا توجد قواعد جدار حماية - الراوتر غير محمي!</div>";
                }
                
                // فحص قوائم العناوين
                $addressLists = $api->comm('/ip/firewall/address-list/print');
                $blacklistCount = 0;
                foreach ($addressLists as $list) {
                    if (isset($list['list']) && strpos($list['list'], 'blacklist') !== false) {
                        $blacklistCount++;
                    }
                }
                
                if ($blacklistCount > 0) {
                    echo "<div style='color: green;'>✅ يوجد $blacklistCount عنوان محظور</div>";
                } else {
                    echo "<div style='color: orange;'>⚠️ لا توجد عناوين محظورة</div>";
                }
                
            } catch (Exception $e) {
                echo "<div style='color: red;'>خطأ في قراءة جدار الحماية: " . $e->getMessage() . "</div>";
            }
            
            $api->disconnect();
            
        } else {
            echo "<div style='color: red;'>❌ فشل الاتصال بـ MikroTik</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>خطأ: " . $e->getMessage() . "</div>";
    }
}

echo "<h2>🛡️ خطوات الحماية المطلوبة:</h2>";
echo "<div style='background: #d1ecf1; padding: 20px; border: 1px solid #bee5eb; border-radius: 5px;'>";

echo "<h3>1. حماية فورية:</h3>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
echo "# حظر العنوان المهاجم
/ip firewall address-list add list=blacklist address=************* comment=\"Brute Force Attack\"

# حذف المستخدم المشبوه
/user remove UIpe

# تقييد API للشبكة المحلية فقط
/ip service set api address=**********/24";
echo "</pre>";

echo "<h3>2. تقوية كلمات المرور:</h3>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
echo "# تغيير كلمة مرور admin
/user set admin password=\"كلمة_مرور_قوية_جداً\"

# تحديث كلمة مرور mohager
/user set mohager password=\"P@\$\$w0rd2025!\"";
echo "</pre>";

echo "<h3>3. تفعيل الحماية من Brute Force:</h3>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
echo "# حماية API من الهجمات
/ip firewall filter add chain=input protocol=tcp dst-port=8728 src-address-list=blacklist action=drop comment=\"Block API Attacks\"

# حماية SSH
/ip firewall filter add chain=input protocol=tcp dst-port=22 src-address-list=blacklist action=drop comment=\"Block SSH Attacks\"

# حماية Winbox
/ip firewall filter add chain=input protocol=tcp dst-port=8291 src-address-list=blacklist action=drop comment=\"Block Winbox Attacks\"";
echo "</pre>";

echo "<h3>4. مراقبة مستمرة:</h3>";
echo "<ul>";
echo "<li>راقب ملفات السجل بانتظام</li>";
echo "<li>فحص المستخدمين الجدد</li>";
echo "<li>مراقبة محاولات الدخول الفاشلة</li>";
echo "<li>تحديث RouterOS بانتظام</li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='final_test.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>اختبار النظام بعد الحماية</a>";
echo "</div>";
?>
