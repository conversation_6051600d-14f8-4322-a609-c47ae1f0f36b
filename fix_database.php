<?php
/**
 * إصلاح مشاكل قاعدة البيانات
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح قاعدة البيانات</h1>";

try {
    // الاتصال بـ root
    $root_conn = new mysqli('localhost', 'root', '');
    
    if ($root_conn->connect_error) {
        throw new Exception("فشل الاتصال بـ root: " . $root_conn->connect_error);
    }
    
    echo "<div style='color: green;'>✅ تم الاتصال بـ root بنجاح</div>";
    
    // حذف المستخدم إذا كان موجوداً (لإعادة إنشائه)
    $root_conn->query("DROP USER IF EXISTS 'mikrosys'@'localhost'");
    echo "<div>🗑️ تم حذف المستخدم السابق (إن وجد)</div>";
    
    // إنشاء المستخدم الجديد
    $create_user = "CREATE USER 'mikrosys'@'localhost' IDENTIFIED BY 'mikrosys@2025'";
    if (!$root_conn->query($create_user)) {
        throw new Exception("فشل في إنشاء المستخدم: " . $root_conn->error);
    }
    echo "<div style='color: green;'>✅ تم إنشاء المستخدم mikrosys</div>";
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $create_db = "CREATE DATABASE IF NOT EXISTS mikrosys CHARACTER SET utf8 COLLATE utf8_general_ci";
    if (!$root_conn->query($create_db)) {
        throw new Exception("فشل في إنشاء قاعدة البيانات: " . $root_conn->error);
    }
    echo "<div style='color: green;'>✅ تم إنشاء/التأكد من قاعدة البيانات mikrosys</div>";
    
    // منح جميع الصلاحيات
    $grant_all = "GRANT ALL PRIVILEGES ON mikrosys.* TO 'mikrosys'@'localhost'";
    if (!$root_conn->query($grant_all)) {
        throw new Exception("فشل في منح الصلاحيات: " . $root_conn->error);
    }
    echo "<div style='color: green;'>✅ تم منح جميع الصلاحيات</div>";
    
    // تحديث الصلاحيات
    $root_conn->query("FLUSH PRIVILEGES");
    echo "<div style='color: green;'>✅ تم تحديث الصلاحيات</div>";
    
    $root_conn->close();
    
    // اختبار الاتصال بالمستخدم الجديد
    echo "<h2>اختبار الاتصال بالمستخدم الجديد:</h2>";
    $test_conn = new mysqli('localhost', 'mikrosys', 'mikrosys@2025', 'mikrosys');
    
    if ($test_conn->connect_error) {
        throw new Exception("فشل الاتصال بالمستخدم الجديد: " . $test_conn->connect_error);
    }
    
    echo "<div style='color: green;'>✅ تم الاتصال بالمستخدم mikrosys بنجاح!</div>";
    
    // إنشاء الجداول الأساسية
    echo "<h2>إنشاء الجداول:</h2>";
    
    // جدول الكروت
    $sql_cards = "CREATE TABLE IF NOT EXISTS hotspot_cards (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(50) NOT NULL,
        profile VARCHAR(50) NOT NULL,
        status ENUM('active', 'used', 'expired') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        used_at TIMESTAMP NULL,
        mikrotik_id VARCHAR(50) NULL,
        notes TEXT NULL,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
    
    if ($test_conn->query($sql_cards)) {
        echo "<div style='color: green;'>✅ تم إنشاء جدول hotspot_cards</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء جدول hotspot_cards: " . $test_conn->error . "</div>";
    }
    
    // جدول البروفايلات
    $sql_profiles = "CREATE TABLE IF NOT EXISTS hotspot_profiles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        session_timeout INT DEFAULT 0,
        idle_timeout INT DEFAULT 0,
        rate_limit VARCHAR(50) DEFAULT '',
        shared_users INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
    
    if ($test_conn->query($sql_profiles)) {
        echo "<div style='color: green;'>✅ تم إنشاء جدول hotspot_profiles</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء جدول hotspot_profiles: " . $test_conn->error . "</div>";
    }
    
    // جدول الإعدادات
    $sql_settings = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
    
    if ($test_conn->query($sql_settings)) {
        echo "<div style='color: green;'>✅ تم إنشاء جدول system_settings</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء جدول system_settings: " . $test_conn->error . "</div>";
    }
    
    // إدراج بروفايل افتراضي
    $default_profile = "INSERT IGNORE INTO hotspot_profiles (name, display_name, session_timeout, idle_timeout, rate_limit, shared_users) 
                      VALUES ('24hours', '24 ساعة', 86400, 0, '1M/1M', 1)";
    
    if ($test_conn->query($default_profile)) {
        echo "<div style='color: green;'>✅ تم إدراج البروفايل الافتراضي</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في إدراج البروفايل الافتراضي: " . $test_conn->error . "</div>";
    }
    
    $test_conn->close();
    
    echo "<hr>";
    echo "<div style='color: green; font-size: 18px; font-weight: bold;'>🎉 تم إصلاح قاعدة البيانات بنجاح!</div>";
    echo "<div>يمكنك الآن العودة إلى النظام واختبار الاتصال.</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-size: 16px;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div>تأكد من أن XAMPP يعمل وأن MySQL متاح.</div>";
}
?>
