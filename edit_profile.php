<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// التحقق من وجود معرف البروفايل
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "معرف البروفايل غير صحيح";
    header('Location: profiles.php');
    exit;
}

$profile_id = intval($_GET['id']);

// جلب بيانات البروفايل
$profile = getProfileById($profile_id);
if (!$profile) {
    $_SESSION['error'] = "البروفايل غير موجود";
    header('Location: profiles.php');
    exit;
}

// معالجة تحديث البروفايل
if ($_POST['action'] == 'update_profile') {
    $display_name = validateInput($_POST['display_name']);
    $session_timeout = intval($_POST['session_timeout']);
    $idle_timeout = intval($_POST['idle_timeout']);
    $rate_limit = validateInput($_POST['rate_limit']);
    $shared_users = intval($_POST['shared_users']);
    
    if (updateProfile($profile_id, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users)) {
        $_SESSION['success'] = "تم تحديث البروفايل بنجاح";
        header('Location: profiles.php');
        exit;
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء تحديث البروفايل";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحرير البروفايل - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="header-content">
                    <div class="logo-container">
                        <img src="assets/img/Logo.png" alt="MikroSys Logo" class="system-logo">
                    </div>
                    <div class="header-text">
                        <h1><i class="fas fa-edit"></i> تحرير البروفايل</h1>
                        <p class="mb-0"><?= htmlspecialchars($profile['display_name']) ?></p>
                    </div>
                    <div class="header-actions">
                        <a href="profiles.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="container p-4">
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-edit"></i> تحرير بيانات البروفايل</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم البروفايل (غير قابل للتغيير)</label>
                                                <input type="text" class="form-control" value="<?= htmlspecialchars($profile['name']) ?>" readonly>
                                                <div class="form-text">لا يمكن تغيير اسم البروفايل بعد الإنشاء</div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">الاسم المعروض</label>
                                                <input type="text" name="display_name" class="form-control" 
                                                       value="<?= htmlspecialchars($profile['display_name']) ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">مدة الجلسة (ثانية)</label>
                                                <div class="input-group">
                                                    <input type="number" name="session_timeout" class="form-control" 
                                                           value="<?= $profile['session_timeout'] ?>" min="0">
                                                    <button type="button" class="btn btn-outline-secondary" onclick="calculateTime('session')">
                                                        <i class="fas fa-calculator"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">
                                                    الحالي: <?= formatTime($profile['session_timeout']) ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">مهلة عدم النشاط (ثانية)</label>
                                                <div class="input-group">
                                                    <input type="number" name="idle_timeout" class="form-control" 
                                                           value="<?= $profile['idle_timeout'] ?>" min="0">
                                                    <button type="button" class="btn btn-outline-secondary" onclick="calculateTime('idle')">
                                                        <i class="fas fa-calculator"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">
                                                    الحالي: <?= formatTime($profile['idle_timeout']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">حد السرعة</label>
                                                <input type="text" name="rate_limit" class="form-control" 
                                                       value="<?= htmlspecialchars($profile['rate_limit']) ?>" 
                                                       placeholder="مثال: 5M/5M">
                                                <div class="form-text">تنسيق: تحميل/رفع (مثال: 10M/2M)</div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">عدد المستخدمين المتزامنين</label>
                                                <input type="number" name="shared_users" class="form-control" 
                                                       value="<?= $profile['shared_users'] ?>" min="1">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save"></i> حفظ التغييرات
                                        </button>
                                        <a href="profiles.php" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- معلومات إضافية -->
                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h6><i class="fas fa-info-circle"></i> معلومات البروفايل</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>تاريخ الإنشاء:</strong> <?= date('Y-m-d H:i', strtotime($profile['created_at'])) ?></p>
                                        <p><strong>عدد الكروت المستخدمة:</strong> 
                                            <span class="badge bg-primary"><?= getCardsCountByProfile($profile['name']) ?></span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>آخر استخدام:</strong> <?= getLastUsageByProfile($profile['name']) ?></p>
                                        <p><strong>الحالة:</strong> 
                                            <span class="badge bg-success">نشط</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مودال حاسبة الوقت -->
    <div class="modal fade" id="timeCalculatorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حاسبة الوقت</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">الأيام</label>
                            <input type="number" id="calc_days" class="form-control" value="0" min="0">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الساعات</label>
                            <input type="number" id="calc_hours" class="form-control" value="0" min="0" max="23">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الدقائق</label>
                            <input type="number" id="calc_minutes" class="form-control" value="0" min="0" max="59">
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">النتيجة بالثواني:</label>
                        <input type="text" id="calc_result" class="form-control" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="applyCalculatedTime()">تطبيق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTimeField = '';
        
        function calculateTime(field) {
            currentTimeField = field;
            const modal = new bootstrap.Modal(document.getElementById('timeCalculatorModal'));
            modal.show();
            updateCalculation();
        }
        
        function updateCalculation() {
            const days = parseInt(document.getElementById('calc_days').value) || 0;
            const hours = parseInt(document.getElementById('calc_hours').value) || 0;
            const minutes = parseInt(document.getElementById('calc_minutes').value) || 0;
            
            const totalSeconds = (days * 24 * 60 * 60) + (hours * 60 * 60) + (minutes * 60);
            document.getElementById('calc_result').value = totalSeconds;
        }
        
        function applyCalculatedTime() {
            const result = document.getElementById('calc_result').value;
            if (currentTimeField === 'session') {
                document.querySelector('input[name="session_timeout"]').value = result;
            } else if (currentTimeField === 'idle') {
                document.querySelector('input[name="idle_timeout"]').value = result;
            }
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('timeCalculatorModal'));
            modal.hide();
        }
        
        // تحديث الحساب عند تغيير القيم
        document.addEventListener('DOMContentLoaded', function() {
            ['calc_days', 'calc_hours', 'calc_minutes'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateCalculation);
            });
        });
    </script>
</body>
</html>
