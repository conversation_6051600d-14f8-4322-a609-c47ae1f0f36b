<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// معالجة حفظ الإعدادات
if ($_POST['action'] == 'save_settings') {
    $mikrotik_host = validateInput($_POST['mikrotik_host']);
    $mikrotik_mac = validateInput($_POST['mikrotik_mac'] ?? '');
    $connection_type = validateInput($_POST['connection_type'] ?? 'ip');
    $mikrotik_user = validateInput($_POST['mikrotik_user']);
    $mikrotik_pass = $_POST['mikrotik_pass']; // لا نقوم بتشفير كلمة المرور هنا
    $mikrotik_port = validateInput($_POST['mikrotik_port'], 'int');

    // التحقق من صحة MAC Address فقط إذا تم اختيار نوع الاتصال MAC وتم إدخال قيمة
    if ($connection_type === 'mac') {
        if (empty($mikrotik_mac)) {
            $_SESSION['error'] = "يرجى إدخال MAC Address أو استخدام زر الاكتشاف";
        } elseif (!isValidMacAddress($mikrotik_mac)) {
            $_SESSION['error'] = "MAC Address غير صحيح. يجب أن يكون بالتنسيق: AA:BB:CC:DD:EE:FF";
        } else {
            $mikrotik_mac = formatMacAddress($mikrotik_mac, ':');
        }
    } else {
        // إذا كان نوع الاتصال IP، تأكد من وجود IP
        if (empty($mikrotik_host)) {
            $_SESSION['error'] = "يرجى إدخال عنوان IP للسيرفر";
        }
    }

    if (!isset($_SESSION['error'])) {
        // حفظ الإعدادات
        setSetting('mikrotik_host', $mikrotik_host);
        setSetting('mikrotik_mac', $mikrotik_mac);
        setSetting('connection_type', $connection_type);
        setSetting('mikrotik_user', $mikrotik_user);
        setSetting('mikrotik_pass', $mikrotik_pass);
        setSetting('mikrotik_port', $mikrotik_port);

        $_SESSION['success'] = "تم حفظ الإعدادات بنجاح";
    }
}

// جلب الإعدادات الحالية
$current_settings = [
    'mikrotik_host' => getSetting('mikrotik_host', MIKROTIK_HOST),
    'mikrotik_mac' => getSetting('mikrotik_mac', defined('MIKROTIK_MAC') ? MIKROTIK_MAC : ''),
    'connection_type' => getSetting('connection_type', defined('MIKROTIK_CONNECTION_TYPE') ? MIKROTIK_CONNECTION_TYPE : 'ip'),
    'mikrotik_user' => getSetting('mikrotik_user', MIKROTIK_USER),
    'mikrotik_pass' => getSetting('mikrotik_pass', MIKROTIK_PASS),
    'mikrotik_port' => getSetting('mikrotik_port', MIKROTIK_PORT)
];

// اختبار الاتصال
$connection_status = checkMikroTikConnection();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .connection-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="header-content">
                    <div class="logo-container">
                        <img src="assets/img/Logo.png" alt="MikroSys Logo" class="system-logo">
                    </div>
                    <div class="header-text">
                        <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                        <p class="mb-0">إعدادات الاتصال بسيرفر المايكروتك</p>
                    </div>
                    <div class="header-actions">
                        <a href="test_mac_connection.php" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-network-wired"></i> اختبار MAC
                        </a>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="container p-4">

                
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show mt-3">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <!-- حالة الاتصال -->
                <div class="connection-status <?= $connection_status ? 'status-connected' : 'status-disconnected' ?>">
                    <?php if ($connection_status): ?>
                        <i class="fas fa-check-circle"></i> الاتصال بسيرفر المايكروتك نشط
                    <?php else: ?>
                        <i class="fas fa-exclamation-triangle"></i> فشل الاتصال بسيرفر المايكروتك
                    <?php endif; ?>
                </div>

                <!-- نموذج الإعدادات -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-server"></i> إعدادات MikroTik</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="save_settings">
                            
                            <!-- نوع الاتصال -->
                            <div class="mb-3">
                                <label class="form-label">نوع الاتصال</label>
                                <select name="connection_type" class="form-control" onchange="toggleConnectionType()">
                                    <option value="ip" <?= $current_settings['connection_type'] === 'ip' ? 'selected' : '' ?>>
                                        🌐 عنوان IP
                                    </option>
                                    <option value="mac" <?= $current_settings['connection_type'] === 'mac' ? 'selected' : '' ?>>
                                        🔗 MAC Address
                                    </option>
                                </select>
                                <div class="form-text">اختر طريقة الاتصال بجهاز MikroTik</div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3" id="ip_field">
                                        <label class="form-label">عنوان IP للسيرفر</label>
                                        <input type="text" name="mikrotik_host" class="form-control"
                                               value="<?= htmlspecialchars($current_settings['mikrotik_host']) ?>"
                                               placeholder="***********">
                                        <div class="form-text">عنوان IP الخاص بجهاز المايكروتك</div>
                                    </div>

                                    <div class="mb-3" id="mac_field" style="display: none;">
                                        <label class="form-label">MAC Address للسيرفر</label>
                                        <div class="input-group">
                                            <input type="text" name="mikrotik_mac" class="form-control"
                                                   value="<?= htmlspecialchars($current_settings['mikrotik_mac']) ?>"
                                                   placeholder="AA:BB:CC:DD:EE:FF">
                                            <button type="button" class="btn btn-outline-secondary" onclick="detectMacAddress()">
                                                <i class="fas fa-search"></i> اكتشاف
                                            </button>
                                        </div>
                                        <div class="form-text">MAC Address الخاص بجهاز MikroTik (سيتم البحث عن IP تلقائياً)</div>
                                        <div id="mac_detection_result" class="mt-2"></div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم المنفذ</label>
                                        <input type="number" name="mikrotik_port" class="form-control" 
                                               value="<?= htmlspecialchars($current_settings['mikrotik_port']) ?>" 
                                               placeholder="8728" min="1" max="65535" required>
                                        <div class="form-text">منفذ API (افتراضي: 8728)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" name="mikrotik_user" class="form-control" 
                                               value="<?= htmlspecialchars($current_settings['mikrotik_user']) ?>" 
                                               placeholder="admin" required>
                                        <div class="form-text">اسم المستخدم للدخول على المايكروتك</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" name="mikrotik_pass" class="form-control" 
                                               value="<?= htmlspecialchars($current_settings['mikrotik_pass']) ?>" 
                                               placeholder="كلمة المرور">
                                        <div class="form-text">كلمة مرور المايكروتك (اتركها فارغة إذا لم تكن محددة)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ الإعدادات
                                </button>
                                <button type="button" class="btn btn-info btn-lg" onclick="testConnection()">
                                    <i class="fas fa-plug"></i> اختبار الاتصال
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-info-circle"></i> معلومات مهمة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-shield-alt"></i> الأمان</h6>
                                <ul class="list-unstyled">
                                    <li>• تأكد من تفعيل API في المايكروتك</li>
                                    <li>• استخدم مستخدم محدود الصلاحيات</li>
                                    <li>• قم بتغيير المنفذ الافتراضي للأمان</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-tools"></i> إعداد المايكروتك</h6>
                                <ul class="list-unstyled">
                                    <li>• IP > Services > API (تفعيل)</li>
                                    <li>• System > Users (إنشاء مستخدم)</li>
                                    <li>• IP > Hotspot (إعداد الهوتسبوت)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات النظام -->
                <div class="card mt-4">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات النظام</h5>
                    </div>
                    <div class="card-body">
                        <?php $stats = getSystemStats(); ?>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h4><?= $stats['total_cards'] ?></h4>
                                        <p>إجمالي الكروت</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h4><?= $stats['active_cards'] ?></h4>
                                        <p>كروت نشطة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <h4><?= $stats['used_cards'] ?></h4>
                                        <p>كروت مستخدمة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h4><?= $stats['today_cards'] ?></h4>
                                        <p>كروت اليوم</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testConnection() {
            // يمكن إضافة AJAX لاختبار الاتصال بدون إعادة تحميل الصفحة
            alert('سيتم اختبار الاتصال...');
            window.location.reload();
        }

        // التحكم في عرض حقول الاتصال
        function toggleConnectionType() {
            const connectionType = document.querySelector('select[name="connection_type"]').value;
            const ipField = document.getElementById('ip_field');
            const macField = document.getElementById('mac_field');

            if (connectionType === 'mac') {
                if (ipField) ipField.style.display = 'none';
                if (macField) macField.style.display = 'block';
                // إزالة required من IP وإضافته للـ MAC
                const ipInput = document.querySelector('input[name="mikrotik_host"]');
                const macInput = document.querySelector('input[name="mikrotik_mac"]');
                if (ipInput) ipInput.removeAttribute('required');
                if (macInput) macInput.setAttribute('required', 'required');
            } else {
                if (ipField) ipField.style.display = 'block';
                if (macField) macField.style.display = 'none';
                // إزالة required من MAC وإضافته للـ IP
                const ipInput = document.querySelector('input[name="mikrotik_host"]');
                const macInput = document.querySelector('input[name="mikrotik_mac"]');
                if (macInput) macInput.removeAttribute('required');
                if (ipInput) ipInput.setAttribute('required', 'required');
            }
        }

        // اكتشاف MAC Address من سيرفر MikroTik مباشرة
        function detectMacAddress() {
            const resultDiv = document.getElementById('mac_detection_result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> جاري الاتصال بسيرفر MikroTik واكتشاف الأجهزة المتصلة...';

            fetch('detect_mikrotik_direct.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.devices.length > 0) {
                        let html = '<div class="alert alert-success">';
                        html += '<h6><i class="fas fa-server"></i> تم الاتصال بسيرفر MikroTik بنجاح!</h6>';

                        if (data.system_info) {
                            html += '<p><strong>اسم الراوتر:</strong> ' + (data.system_info.identity || 'MikroTik') + '</p>';
                            if (data.system_info.version) {
                                html += '<p><strong>الإصدار:</strong> ' + data.system_info.version + '</p>';
                            }
                        }

                        html += '<h6>الأجهزة المتصلة (' + data.count + ' جهاز):</h6>';
                        html += '<div style="max-height: 300px; overflow-y: auto;"><ul class="list-unstyled">';

                        data.devices.forEach(device => {
                            let deviceInfo = '';
                            if (device.type === 'Router Interface') {
                                deviceInfo = '<span class="badge bg-primary">واجهة الراوتر</span>';
                            } else if (device.type === 'DHCP Client') {
                                deviceInfo = '<span class="badge bg-success">عميل DHCP</span>';
                            } else if (device.type === 'Wireless Client') {
                                deviceInfo = '<span class="badge bg-info">عميل لاسلكي</span>';
                            } else {
                                deviceInfo = '<span class="badge bg-secondary">جهاز متصل</span>';
                            }

                            html += `<li class="mb-2 p-2 border rounded">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${device.mac}</strong>
                                        ${device.ip ? ' - ' + device.ip : ''}
                                        ${device.hostname ? ' (' + device.hostname + ')' : ''}
                                        <br>
                                        ${deviceInfo}
                                        ${device.interface ? ' - ' + device.interface : ''}
                                        ${device.signal_strength ? ' - إشارة: ' + device.signal_strength : ''}
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            onclick="selectMacAddress('${device.mac}', '${device.ip || ''}')">
                                        اختيار
                                    </button>
                                </div>
                            </li>`;
                        });

                        html += '</ul></div>';

                        if (data.stats) {
                            html += '<hr><small class="text-muted">';
                            html += 'الإحصائيات: ';
                            html += data.stats.router_interfaces + ' واجهة راوتر، ';
                            html += data.stats.arp_entries + ' جهاز ARP، ';
                            html += data.stats.dhcp_leases + ' عميل DHCP، ';
                            html += data.stats.wireless_clients + ' عميل لاسلكي';
                            html += '</small>';
                        }

                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        let errorMsg = data.error || 'لم يتم العثور على أجهزة متصلة';
                        resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> ' + errorMsg + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle"></i> خطأ في الاتصال: ' + error.message + '<br><small>تأكد من صحة إعدادات MikroTik وأن API مفعل</small></div>';
                });
        }

        // اختيار MAC Address
        function selectMacAddress(mac, ip) {
            document.querySelector('input[name="mikrotik_mac"]').value = mac;
            document.querySelector('input[name="mikrotik_host"]').value = ip;
            document.getElementById('mac_detection_result').innerHTML =
                '<div class="alert alert-info">تم اختيار: <strong>' + mac + '</strong> - ' + ip + '</div>';
        }

        // تشغيل التحكم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            toggleConnectionType();
        });
    </script>
</body>
</html>
