# دليل البروفايلات والمدة المخصصة - MikroSys

## 🎯 الميزات الجديدة

### 1. إدارة البروفايلات
- ✅ إنشاء بروفايلات سرعة مخصصة
- ✅ تحرير البروفايلات الموجودة
- ✅ حذف البروفايلات غير المستخدمة
- ✅ قوالب سرعة جاهزة
- ✅ حاسبة الوقت المتقدمة

### 2. تحديد مدة الكارت المخصصة
- ✅ تحديد بالأيام والساعات والدقائق
- ✅ معاينة فورية للمدة الإجمالية
- ✅ مرونة كاملة في التحديد
- ✅ حفظ تلقائي للإعدادات

## 📁 الملفات الجديدة

### صفحات البروفايلات
- `profiles.php` - إدارة البروفايلات الرئيسية
- `edit_profile.php` - تحرير البروفايل
- `test_profiles.php` - اختبار الميزات الجديدة

### التحديثات على الملفات الموجودة
- `index.php` - إضافة خيار المدة المخصصة
- `functions.php` - دوال جديدة للبروفايلات
- `assets/style.css` - أنماط محدثة

## 🚀 كيفية الاستخدام

### إدارة البروفايلات

#### 1. الوصول لصفحة البروفايلات
```
http://localhost/mikrosys/profiles.php
```

#### 2. إنشاء بروفايل جديد
1. املأ النموذج في الجانب الأيسر
2. استخدم حاسبة الوقت لتحديد المدة
3. اختر من قوالب السرعة الجاهزة
4. اضغط "إضافة البروفايل"

#### 3. تحرير بروفايل موجود
1. اضغط زر التحرير بجانب البروفايل
2. عدّل البيانات المطلوبة
3. احفظ التغييرات

### تحديد مدة الكارت المخصصة

#### في الصفحة الرئيسية:
1. اختر عدد الكروت
2. اختر البروفايل
3. حدد المدة:
   - **الأيام:** 0-365
   - **الساعات:** 0-23
   - **الدقائق:** 0-59
4. شاهد المعاينة الفورية
5. اضغط "إنشاء الكروت"

## 🔧 الدوال الجديدة

### دوال البروفايلات
```php
// إضافة بروفايل جديد
addProfile($name, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users)

// تحديث بروفايل
updateProfile($id, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users)

// حذف بروفايل
deleteProfile($id)

// الحصول على بروفايل بالمعرف
getProfileById($id)
```

### دوال الوقت
```php
// تنسيق الوقت
formatTime($seconds) // مثال: 3600 → "ساعة واحدة"

// تحويل إلى ثوان
timeToSeconds($days, $hours, $minutes) // مثال: (1, 2, 30) → 95400
```

### دوال الإحصائيات
```php
// عدد الكروت لبروفايل معين
getCardsCountByProfile($profile_name)

// آخر استخدام لبروفايل
getLastUsageByProfile($profile_name)
```

## 📊 قوالب السرعة المتاحة

| السرعة | الوصف |
|---------|--------|
| 1M/512K | أساسي - للاستخدام البسيط |
| 2M/1M | عادي - للتصفح العام |
| 5M/2M | جيد - للفيديو بجودة متوسطة |
| 10M/5M | ممتاز - للفيديو عالي الجودة |
| 20M/10M | عالي - للألعاب والتحميل |
| 50M/25M | فائق - للاستخدام المكثف |
| 100M/50M | احترافي - للشركات |

## ⏰ قوالب الوقت المتاحة

| المدة | الثواني | الاستخدام |
|-------|---------|-----------|
| 30 دقيقة | 1800 | تجريبي |
| ساعة واحدة | 3600 | قصير |
| ساعتين | 7200 | متوسط |
| 6 ساعات | 21600 | طويل |
| 24 ساعة | 86400 | يومي |
| أسبوع | 604800 | أسبوعي |
| شهر | 2592000 | شهري |
| بدون حد | 0 | مفتوح |

## 🎨 واجهة المستخدم

### صفحة البروفايلات
- **الجانب الأيسر:** نموذج إضافة بروفايل جديد
- **الجانب الأيمن:** قائمة البروفايلات الموجودة
- **حاسبة الوقت:** مودال لحساب المدة بسهولة
- **قوالب السرعة:** قائمة منسدلة بالسرعات الشائعة

### الصفحة الرئيسية
- **خيار المدة:** ثلاثة حقول (أيام، ساعات، دقائق)
- **معاينة فورية:** عرض المدة الإجمالية
- **زر البروفايلات:** وصول سريع لإدارة البروفايلات

## 🔍 اختبار الميزات

### اختبار شامل
```
http://localhost/mikrosys/test_profiles.php
```

### اختبارات فردية
1. **البروفايلات:** `profiles.php`
2. **التحرير:** `edit_profile.php?id=1`
3. **المدة المخصصة:** `index.php`

## 📝 أمثلة عملية

### مثال 1: بروفايل للطلاب
- **الاسم:** student_2hours
- **الاسم المعروض:** طلاب - ساعتين
- **مدة الجلسة:** 7200 ثانية (2 ساعة)
- **السرعة:** 5M/2M
- **المستخدمين:** 1

### مثال 2: بروفايل للزوار
- **الاسم:** visitor_30min
- **الاسم المعروض:** زوار - 30 دقيقة
- **مدة الجلسة:** 1800 ثانية (30 دقيقة)
- **السرعة:** 2M/1M
- **المستخدمين:** 1

### مثال 3: بروفايل VIP
- **الاسم:** vip_unlimited
- **الاسم المعروض:** VIP - بدون حدود
- **مدة الجلسة:** 0 (بدون حد)
- **السرعة:** 100M/50M
- **المستخدمين:** 3

## 🛠️ نصائح للاستخدام

### إنشاء البروفايلات
1. **ابدأ بالبروفايلات الأساسية** (1 ساعة، 24 ساعة، أسبوع)
2. **استخدم أسماء واضحة** (مثل: 5mbps_2hours)
3. **اختبر البروفايل** قبل الاستخدام الفعلي
4. **احفظ نسخة احتياطية** من إعدادات البروفايلات

### تحديد المدة
1. **للاستخدام القصير:** استخدم الساعات والدقائق
2. **للاستخدام الطويل:** استخدم الأيام
3. **للاختبار:** ابدأ بـ 30 دقيقة
4. **للعملاء:** استخدم 24 ساعة أو أكثر

### إدارة السرعة
1. **للمقاهي:** 5M/2M أو 10M/5M
2. **للمكاتب:** 20M/10M أو أعلى
3. **للفنادق:** 10M/5M للعادي، 50M/25M للـ VIP
4. **للمدارس:** 2M/1M أو 5M/2M

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### 1. لا يمكن إنشاء بروفايل
- تحقق من اتصال قاعدة البيانات
- تأكد من عدم تكرار اسم البروفايل
- تحقق من صحة البيانات المدخلة

#### 2. لا تظهر البروفايلات
- تحقق من وجود جدول hotspot_profiles
- تأكد من وجود بيانات في الجدول
- تحقق من دالة getAvailableProfiles()

#### 3. مشكلة في حاسبة الوقت
- تأكد من تحميل JavaScript
- تحقق من وجود Bootstrap
- اختبر في متصفح آخر

### حلول سريعة

#### إعادة إنشاء البروفايلات الافتراضية
```sql
INSERT IGNORE INTO hotspot_profiles (name, display_name, session_timeout, idle_timeout, rate_limit, shared_users) VALUES
('1hour', 'ساعة واحدة', 3600, 300, '5M/2M', 1),
('24hours', '24 ساعة', 86400, 900, '10M/5M', 1),
('7days', 'أسبوع', 604800, 1800, '15M/10M', 2);
```

#### اختبار الدوال
```php
// في test_profiles.php
echo formatTime(3600); // يجب أن يعرض "ساعة واحدة"
echo timeToSeconds(1, 2, 30); // يجب أن يعرض 95400
```

## 📞 الدعم

إذا واجهت مشاكل:
1. اختبر النظام باستخدام `test_profiles.php`
2. تحقق من سجلات الأخطاء في `logs/error.log`
3. تأكد من إعدادات قاعدة البيانات
4. اختبر على متصفح آخر

---

**الميزات الجديدة تمنحك مرونة كاملة في إدارة كروت الهوتسبوت! 🎉**
