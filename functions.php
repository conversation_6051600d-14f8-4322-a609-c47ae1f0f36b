<?php
// تجنب إعادة التضمين
if (!defined('FUNCTIONS_LOADED')) {
    define('FUNCTIONS_LOADED', true);

    // تضمين الملفات المطلوبة
    if (file_exists('config.php')) {
        require_once 'config.php';
    }

    if (file_exists('mikrotik_api.php')) {
        require_once 'mikrotik_api.php';
    }
}

// دالة لتوليد اسم مستخدم عشوائي
function generateUsername($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $username = '';
    for ($i = 0; $i < $length; $i++) {
        $username .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $username;
}

// دالة لتوليد كلمة مرور عشوائية
function generatePassword($length = 6) {
    $characters = '0123456789';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $password;
}

// دالة لإنشاء مستخدم في MikroTik
function createMikroTikUser($username, $password, $profile) {
    try {
        $api = new RouterosAPI();
        $api->debug = false;
        
        if (!$api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            throw new Exception("فشل الاتصال بـ MikroTik");
        }
        
        // إنشاء المستخدم
        $api->write('/ip/hotspot/user/add', false);
        $api->write('=name=' . $username, false);
        $api->write('=password=' . $password, false);
        $api->write('=profile=' . $profile);
        
        $response = $api->read();
        $api->disconnect();
        
        if (isset($response[0]) && isset($response[0]['!trap'])) {
            throw new Exception("خطأ في إنشاء المستخدم: " . $response[0]['message']);
        }
        
        return true;
        
    } catch (Exception $e) {
        logError("Error creating MikroTik user: " . $e->getMessage());
        return false;
    }
}

// دالة لحذف مستخدم من MikroTik
function deleteMikroTikUser($username) {
    try {
        $api = new RouterosAPI();
        $api->debug = false;
        
        if (!$api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            throw new Exception("فشل الاتصال بـ MikroTik");
        }
        
        // البحث عن المستخدم
        $api->write('/ip/hotspot/user/print', false);
        $api->write('?name=' . $username);
        $users = $api->read();
        
        if (!empty($users)) {
            $user_id = $users[0]['.id'];
            
            // حذف المستخدم
            $api->write('/ip/hotspot/user/remove', false);
            $api->write('=.id=' . $user_id);
            $api->read();
        }
        
        $api->disconnect();
        return true;
        
    } catch (Exception $e) {
        logError("Error deleting MikroTik user: " . $e->getMessage());
        return false;
    }
}

// دالة لتوليد كروت الهوتسبوت
function generateHotspotCards($count, $profile, $validity_hours) {
    global $conn;
    
    $generated_cards = [];
    $expires_at = date('Y-m-d H:i:s', time() + ($validity_hours * 3600));
    
    for ($i = 0; $i < $count; $i++) {
        // توليد اسم المستخدم وكلمة المرور
        do {
            $username = generateUsername();
            $check = mysqli_query($conn, "SELECT id FROM hotspot_cards WHERE username = '$username'");
        } while (mysqli_num_rows($check) > 0);
        
        $password = generatePassword();
        
        // إنشاء المستخدم في MikroTik
        if (createMikroTikUser($username, $password, $profile)) {
            // حفظ الكارت في قاعدة البيانات
            $sql = "INSERT INTO hotspot_cards (username, password, profile, expires_at) 
                    VALUES ('$username', '$password', '$profile', '$expires_at')";
            
            if (mysqli_query($conn, $sql)) {
                $generated_cards[] = [
                    'username' => $username,
                    'password' => $password,
                    'profile' => $profile,
                    'expires_at' => $expires_at
                ];
            } else {
                // إذا فشل حفظ الكارت، احذف المستخدم من MikroTik
                deleteMikroTikUser($username);
                logError("Failed to save card to database: " . mysqli_error($conn));
            }
        } else {
            logError("Failed to create MikroTik user: $username");
        }
    }
    
    return $generated_cards;
}

// دالة للحصول على جميع الكروت
function getAllCards($limit = null) {
    global $conn;

    if (!$conn) {
        return [];
    }

    try {
        $sql = "SELECT * FROM hotspot_cards ORDER BY created_at DESC";
        if ($limit && is_numeric($limit)) {
            $sql .= " LIMIT " . intval($limit);
        }

        $result = $conn->query($sql);
        $cards = [];

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                // تحديث حالة الكارت بناءً على تاريخ الانتهاء
                if ($row['status'] == 'active' && strtotime($row['expires_at']) < time()) {
                    updateCardStatus($row['id'], 'expired');
                    $row['status'] = 'expired';
                }
                $cards[] = $row;
            }
        }

        return $cards;

    } catch (Exception $e) {
        logError("Error getting cards: " . $e->getMessage());
        return [];
    }
}

// دالة لتحديث حالة الكارت
function updateCardStatus($card_id, $status) {
    global $conn;
    
    $sql = "UPDATE hotspot_cards SET status = '$status'";
    if ($status == 'used') {
        $sql .= ", used_at = NOW()";
    }
    $sql .= " WHERE id = $card_id";
    
    return mysqli_query($conn, $sql);
}

// دالة للحصول على عدد الكروت حسب الحالة
function getCardCount($status = 'all') {
    global $conn;

    if (!$conn) {
        return 0;
    }

    try {
        if ($status == 'all') {
            $sql = "SELECT COUNT(*) as count FROM hotspot_cards";
            $result = $conn->query($sql);
        } else {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM hotspot_cards WHERE status = ?");
            $stmt->bind_param('s', $status);
            $stmt->execute();
            $result = $stmt->get_result();
        }

        if ($result) {
            $row = $result->fetch_assoc();
            return $row['count'] ?? 0;
        }

        return 0;

    } catch (Exception $e) {
        logError("Error getting card count: " . $e->getMessage());
        return 0;
    }
}

// دالة لحذف كارت
function deleteCard($card_id) {
    global $conn;
    
    // الحصول على بيانات الكارت
    $result = mysqli_query($conn, "SELECT username FROM hotspot_cards WHERE id = $card_id");
    if ($row = mysqli_fetch_assoc($result)) {
        $username = $row['username'];
        
        // حذف المستخدم من MikroTik
        deleteMikroTikUser($username);
        
        // حذف الكارت من قاعدة البيانات
        $sql = "DELETE FROM hotspot_cards WHERE id = $card_id";
        return mysqli_query($conn, $sql);
    }
    
    return false;
}

// دالة للحصول على نص الحالة
function getStatusText($status) {
    switch ($status) {
        case 'active':
            return 'نشط';
        case 'used':
            return 'مستخدم';
        case 'expired':
            return 'منتهي';
        default:
            return 'غير معروف';
    }
}

// دالة للحصول على البروفايلات المتاحة
function getAvailableProfiles() {
    global $conn;
    
    $result = mysqli_query($conn, "SELECT * FROM hotspot_profiles ORDER BY name");
    $profiles = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $profiles[] = $row;
    }
    
    return $profiles;
}

// دالة لتنظيف الكروت المنتهية الصلاحية
function cleanupExpiredCards() {
    global $conn;
    
    // تحديث حالة الكروت المنتهية
    $sql = "UPDATE hotspot_cards SET status = 'expired' 
            WHERE status = 'active' AND expires_at < NOW()";
    mysqli_query($conn, $sql);
    
    // حذف الكروت المنتهية من MikroTik (اختياري)
    $result = mysqli_query($conn, "SELECT username FROM hotspot_cards WHERE status = 'expired'");
    while ($row = mysqli_fetch_assoc($result)) {
        deleteMikroTikUser($row['username']);
    }
}

// دالة للحصول على إحصائيات النظام
function getSystemStats() {
    global $conn;
    
    $stats = [];
    
    // إجمالي الكروت
    $result = mysqli_query($conn, "SELECT COUNT(*) as total FROM hotspot_cards");
    $stats['total_cards'] = mysqli_fetch_assoc($result)['total'];
    
    // الكروت النشطة
    $result = mysqli_query($conn, "SELECT COUNT(*) as active FROM hotspot_cards WHERE status = 'active'");
    $stats['active_cards'] = mysqli_fetch_assoc($result)['active'];
    
    // الكروت المستخدمة
    $result = mysqli_query($conn, "SELECT COUNT(*) as used FROM hotspot_cards WHERE status = 'used'");
    $stats['used_cards'] = mysqli_fetch_assoc($result)['used'];
    
    // الكروت المنتهية
    $result = mysqli_query($conn, "SELECT COUNT(*) as expired FROM hotspot_cards WHERE status = 'expired'");
    $stats['expired_cards'] = mysqli_fetch_assoc($result)['expired'];
    
    // الكروت المنشأة اليوم
    $result = mysqli_query($conn, "SELECT COUNT(*) as today FROM hotspot_cards WHERE DATE(created_at) = CURDATE()");
    $stats['today_cards'] = mysqli_fetch_assoc($result)['today'];
    
    return $stats;
}

// دالة للتحقق من صحة البيانات
function validateInput($data, $type = 'string') {
    switch ($type) {
        case 'int':
            return filter_var($data, FILTER_VALIDATE_INT);
        case 'email':
            return filter_var($data, FILTER_VALIDATE_EMAIL);
        case 'string':
        default:
            return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
}

// دالة للتحقق من الاتصال بـ MikroTik
function checkMikroTikConnection() {
    try {
        if (!file_exists('mikrotik_api.php')) {
            return false;
        }

        require_once 'mikrotik_api.php';
        $api = new RouterosAPI();
        $api->debug = false;

        $connection_info = getMikroTikConnectionInfo();
        if ($api->connect($connection_info['host'], MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            $api->disconnect();
            return true;
        }

        return false;
    } catch (Exception $e) {
        logError("MikroTik connection check failed: " . $e->getMessage());
        return false;
    }
}

// دالة لإضافة بروفايل جديد
function addProfile($name, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users) {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        // إضافة البروفايل في قاعدة البيانات
        $stmt = $conn->prepare("INSERT INTO hotspot_profiles (name, display_name, session_timeout, idle_timeout, rate_limit, shared_users) VALUES (?, ?, ?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param('ssiisi', $name, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users);
            $result = $stmt->execute();
            $stmt->close();

            if ($result) {
                // إضافة البروفايل في MikroTik تلقائياً
                $mikrotik_result = createMikroTikProfile($name, $session_timeout, $idle_timeout, $rate_limit, $shared_users);
                if (!$mikrotik_result) {
                    logError("Profile added to database but failed to create in MikroTik: $name");
                }
                return true;
            }
        }
    } catch (Exception $e) {
        logError("Error adding profile: " . $e->getMessage());
    }

    return false;
}

// دالة لتحديث بروفايل
function updateProfile($id, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users) {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        // الحصول على اسم البروفايل أولاً
        $stmt = $conn->prepare("SELECT name FROM hotspot_profiles WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param('i', $id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $profile = $result->fetch_assoc();
                $profile_name = $profile['name'];
                $stmt->close();

                // تحديث البروفايل في قاعدة البيانات
                $update_stmt = $conn->prepare("UPDATE hotspot_profiles SET display_name = ?, session_timeout = ?, idle_timeout = ?, rate_limit = ?, shared_users = ? WHERE id = ?");
                if ($update_stmt) {
                    $update_stmt->bind_param('siisii', $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users, $id);
                    $update_result = $update_stmt->execute();
                    $update_stmt->close();

                    if ($update_result) {
                        // تحديث البروفايل في MikroTik
                        $mikrotik_result = createMikroTikProfile($profile_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users);
                        if (!$mikrotik_result) {
                            logError("Profile updated in database but failed to update in MikroTik: $profile_name");
                        }
                        return true;
                    }
                }
            } else {
                $stmt->close();
            }
        }
    } catch (Exception $e) {
        logError("Error updating profile: " . $e->getMessage());
    }

    return false;
}

// دالة لحذف بروفايل
function deleteProfile($id) {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        // الحصول على اسم البروفايل أولاً
        $name_stmt = $conn->prepare("SELECT name FROM hotspot_profiles WHERE id = ?");
        if ($name_stmt) {
            $name_stmt->bind_param('i', $id);
            $name_stmt->execute();
            $name_result = $name_stmt->get_result();

            if ($name_result && $name_result->num_rows > 0) {
                $profile = $name_result->fetch_assoc();
                $profile_name = $profile['name'];
                $name_stmt->close();

                // التحقق من عدم استخدام البروفايل في كروت موجودة
                $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM hotspot_cards WHERE profile = ?");
                if ($check_stmt) {
                    $check_stmt->bind_param('s', $profile_name);
                    $check_stmt->execute();
                    $result = $check_stmt->get_result();
                    $row = $result->fetch_assoc();
                    $check_stmt->close();

                    if ($row['count'] > 0) {
                        logError("Cannot delete profile: still in use by " . $row['count'] . " cards");
                        return false;
                    }
                }

                // حذف البروفايل من قاعدة البيانات
                $delete_stmt = $conn->prepare("DELETE FROM hotspot_profiles WHERE id = ?");
                if ($delete_stmt) {
                    $delete_stmt->bind_param('i', $id);
                    $delete_result = $delete_stmt->execute();
                    $delete_stmt->close();

                    if ($delete_result) {
                        // حذف البروفايل من MikroTik
                        $mikrotik_result = deleteMikroTikProfile($profile_name);
                        if (!$mikrotik_result) {
                            logError("Profile deleted from database but failed to delete from MikroTik: $profile_name");
                        }
                        return true;
                    }
                }
            } else {
                $name_stmt->close();
            }
        }
    } catch (Exception $e) {
        logError("Error deleting profile: " . $e->getMessage());
    }

    return false;
}

// دالة لتنسيق الوقت
function formatTime($seconds) {
    if ($seconds == 0) {
        return 'بدون حد';
    }

    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);

    $parts = [];

    if ($days > 0) {
        $parts[] = $days . ' يوم';
    }
    if ($hours > 0) {
        $parts[] = $hours . ' ساعة';
    }
    if ($minutes > 0) {
        $parts[] = $minutes . ' دقيقة';
    }

    return implode(' و ', $parts);
}

// دالة لتحويل الوقت إلى ثوان
function timeToSeconds($days = 0, $hours = 0, $minutes = 0) {
    return ($days * 24 * 60 * 60) + ($hours * 60 * 60) + ($minutes * 60);
}

// دالة للحصول على بروفايل بالمعرف
function getProfileById($id) {
    global $conn;

    if (!$conn) {
        return null;
    }

    try {
        $stmt = $conn->prepare("SELECT * FROM hotspot_profiles WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param('i', $id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $profile = $result->fetch_assoc();
                $stmt->close();
                return $profile;
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        logError("Error getting profile by ID: " . $e->getMessage());
    }

    return null;
}

// دالة للحصول على عدد الكروت المستخدمة لبروفايل معين
function getCardsCountByProfile($profile_name) {
    global $conn;

    if (!$conn) {
        return 0;
    }

    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM hotspot_cards WHERE profile = ?");
        if ($stmt) {
            $stmt->bind_param('s', $profile_name);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result) {
                $row = $result->fetch_assoc();
                $stmt->close();
                return $row['count'] ?? 0;
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        logError("Error getting cards count by profile: " . $e->getMessage());
    }

    return 0;
}

// دالة للحصول على آخر استخدام لبروفايل معين
function getLastUsageByProfile($profile_name) {
    global $conn;

    if (!$conn) {
        return 'غير معروف';
    }

    try {
        $stmt = $conn->prepare("SELECT MAX(created_at) as last_used FROM hotspot_cards WHERE profile = ?");
        if ($stmt) {
            $stmt->bind_param('s', $profile_name);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result) {
                $row = $result->fetch_assoc();
                $stmt->close();

                if ($row['last_used']) {
                    return date('Y-m-d H:i', strtotime($row['last_used']));
                }
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        logError("Error getting last usage by profile: " . $e->getMessage());
    }

    return 'لم يستخدم بعد';
}

// دالة للحصول على قوالب سرعة شائعة
function getSpeedTemplates() {
    return [
        '1M/512K' => '1 ميجا تحميل / 512 كيلو رفع',
        '2M/1M' => '2 ميجا تحميل / 1 ميجا رفع',
        '5M/2M' => '5 ميجا تحميل / 2 ميجا رفع',
        '10M/5M' => '10 ميجا تحميل / 5 ميجا رفع',
        '20M/10M' => '20 ميجا تحميل / 10 ميجا رفع',
        '50M/25M' => '50 ميجا تحميل / 25 ميجا رفع',
        '100M/50M' => '100 ميجا تحميل / 50 ميجا رفع'
    ];
}

// دالة للحصول على قوالب وقت شائعة
function getTimeTemplates() {
    return [
        '1800' => '30 دقيقة',
        '3600' => 'ساعة واحدة',
        '7200' => 'ساعتين',
        '10800' => '3 ساعات',
        '21600' => '6 ساعات',
        '43200' => '12 ساعة',
        '86400' => '24 ساعة',
        '604800' => 'أسبوع',
        '2592000' => 'شهر',
        '0' => 'بدون حد زمني'
    ];
}

// دالة لإنشاء بروفايل في MikroTik
function createMikroTikProfile($name, $session_timeout, $idle_timeout, $rate_limit, $shared_users) {
    try {
        if (!file_exists('mikrotik_api.php')) {
            logError("MikroTik API file not found");
            return false;
        }

        require_once 'mikrotik_api.php';
        $api = new RouterosAPI();
        $api->debug = false;

        $connection_info = getMikroTikConnectionInfo();
        if (!$api->connect($connection_info['host'], MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            logError("Failed to connect to MikroTik for profile creation. Host: " . $connection_info['host']);
            return false;
        }

        // تحضير معاملات البروفايل
        $profile_params = [
            'name' => $name,
            'shared-users' => $shared_users
        ];

        // إضافة session-timeout إذا كان محدد
        if ($session_timeout > 0) {
            $profile_params['session-timeout'] = formatTimeForMikroTik($session_timeout);
        }

        // إضافة idle-timeout إذا كان محدد
        if ($idle_timeout > 0) {
            $profile_params['idle-timeout'] = formatTimeForMikroTik($idle_timeout);
        }

        // إضافة rate-limit إذا كان محدد
        if (!empty($rate_limit)) {
            $profile_params['rate-limit'] = $rate_limit;
        }

        // التحقق من وجود البروفايل أولاً
        $existing = $api->comm('/ip/hotspot/user/profile/print', [
            '?name' => $name
        ]);

        if (!empty($existing)) {
            // البروفايل موجود، نحدثه
            $api->comm('/ip/hotspot/user/profile/set', array_merge([
                '.id' => $existing[0]['.id']
            ], $profile_params));

            logError("Updated existing MikroTik profile: $name");
        } else {
            // إنشاء بروفايل جديد
            $api->comm('/ip/hotspot/user/profile/add', $profile_params);
            logError("Created new MikroTik profile: $name");
        }

        $api->disconnect();
        return true;

    } catch (Exception $e) {
        logError("Error creating MikroTik profile '$name': " . $e->getMessage());
        return false;
    }
}

// دالة لتنسيق الوقت لـ MikroTik
function formatTimeForMikroTik($seconds) {
    if ($seconds <= 0) {
        return '00:00:00';
    }

    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;

    return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
}

// دالة لمزامنة جميع البروفايلات مع MikroTik
function syncAllProfilesToMikroTik() {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        $result = $conn->query("SELECT * FROM hotspot_profiles");
        if (!$result) {
            return false;
        }

        $success_count = 0;
        $total_count = 0;

        while ($profile = $result->fetch_assoc()) {
            $total_count++;
            if (createMikroTikProfile(
                $profile['name'],
                $profile['session_timeout'],
                $profile['idle_timeout'],
                $profile['rate_limit'],
                $profile['shared_users']
            )) {
                $success_count++;
            }
        }

        logError("Synced $success_count/$total_count profiles to MikroTik");
        return $success_count;

    } catch (Exception $e) {
        logError("Error syncing profiles to MikroTik: " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على البروفايلات من MikroTik
function getMikroTikProfiles() {
    try {
        if (!file_exists('mikrotik_api.php')) {
            return [];
        }

        require_once 'mikrotik_api.php';
        $api = new RouterosAPI();
        $api->debug = false;

        $connection_info = getMikroTikConnectionInfo();
        if (!$api->connect($connection_info['host'], MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            return [];
        }

        $profiles = $api->comm('/ip/hotspot/user/profile/print');
        $api->disconnect();

        return $profiles ?: [];

    } catch (Exception $e) {
        logError("Error getting MikroTik profiles: " . $e->getMessage());
        return [];
    }
}

// دالة لحذف بروفايل من MikroTik
function deleteMikroTikProfile($name) {
    try {
        if (!file_exists('mikrotik_api.php')) {
            return false;
        }

        require_once 'mikrotik_api.php';
        $api = new RouterosAPI();
        $api->debug = false;

        $connection_info = getMikroTikConnectionInfo();
        if (!$api->connect($connection_info['host'], MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            return false;
        }

        // البحث عن البروفايل
        $existing = $api->comm('/ip/hotspot/user/profile/print', [
            '?name' => $name
        ]);

        if (!empty($existing)) {
            // حذف البروفايل
            $api->comm('/ip/hotspot/user/profile/remove', [
                '.id' => $existing[0]['.id']
            ]);

            logError("Deleted MikroTik profile: $name");
            $api->disconnect();
            return true;
        }

        $api->disconnect();
        return false;

    } catch (Exception $e) {
        logError("Error deleting MikroTik profile '$name': " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على معلومات الاتصال بـ MikroTik
function getMikroTikConnectionInfo() {
    // قراءة الإعدادات من قاعدة البيانات أولاً، ثم من الثوابت كبديل
    $connection_type = getSetting('connection_type', defined('MIKROTIK_CONNECTION_TYPE') ? MIKROTIK_CONNECTION_TYPE : 'ip');
    $host = getSetting('mikrotik_host', MIKROTIK_HOST);
    $mac_address = getSetting('mikrotik_mac', defined('MIKROTIK_MAC') ? MIKROTIK_MAC : '');

    // إذا كان نوع الاتصال MAC ولدينا MAC Address
    if ($connection_type === 'mac' && !empty($mac_address)) {
        // محاولة العثور على IP من MAC Address
        try {
            $ip_from_mac = findIpFromMac($mac_address);
            if ($ip_from_mac) {
                $host = $ip_from_mac;
                logError("Found IP $ip_from_mac for MAC " . $mac_address);
            } else {
                logError("Could not find IP for MAC " . $mac_address . ", using configured IP: " . $host);
            }
        } catch (Exception $e) {
            logError("Error finding IP from MAC: " . $e->getMessage() . ", using configured IP: " . $host);
        }
    }

    return [
        'host' => $host,
        'connection_type' => $connection_type,
        'original_host' => getSetting('mikrotik_host', MIKROTIK_HOST),
        'mac_address' => $mac_address
    ];
}

// دالة للعثور على IP من MAC Address
function findIpFromMac($mac_address) {
    $mac_address = strtolower(str_replace([':', '-'], '', $mac_address));

    // طرق مختلفة للعثور على IP من MAC
    $methods = [
        'arp_table',
        'ping_sweep',
        'nmap_scan',
        'neighbor_discovery'
    ];

    foreach ($methods as $method) {
        $ip = call_user_func("findIpFromMac_$method", $mac_address);
        if ($ip) {
            return $ip;
        }
    }

    return null;
}

// البحث في جدول ARP
function findIpFromMac_arp_table($mac_address) {
    try {
        // Windows
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $output = shell_exec('arp -a');
            if ($output) {
                $lines = explode("\n", $output);
                foreach ($lines as $line) {
                    if (preg_match('/(\d+\.\d+\.\d+\.\d+)\s+([a-f0-9-]{17})/i', $line, $matches)) {
                        $found_mac = strtolower(str_replace('-', '', $matches[2]));
                        if ($found_mac === $mac_address) {
                            return $matches[1];
                        }
                    }
                }
            }
        } else {
            // Linux/Unix
            $output = shell_exec('arp -a');
            if ($output) {
                $lines = explode("\n", $output);
                foreach ($lines as $line) {
                    if (preg_match('/\((\d+\.\d+\.\d+\.\d+)\) at ([a-f0-9:]{17})/i', $line, $matches)) {
                        $found_mac = strtolower(str_replace(':', '', $matches[2]));
                        if ($found_mac === $mac_address) {
                            return $matches[1];
                        }
                    }
                }
            }
        }
    } catch (Exception $e) {
        logError("Error in ARP table search: " . $e->getMessage());
    }

    return null;
}

// البحث عبر ping sweep
function findIpFromMac_ping_sweep($mac_address) {
    try {
        // الحصول على شبكة محلية
        $local_ip = getLocalIP();
        if (!$local_ip) {
            return null;
        }

        $network = substr($local_ip, 0, strrpos($local_ip, '.'));

        // ping عدة عناوين في الشبكة المحلية
        for ($i = 1; $i <= 254; $i++) {
            $ip = "$network.$i";

            // ping سريع
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                $ping_result = shell_exec("ping -n 1 -w 1000 $ip 2>nul");
            } else {
                $ping_result = shell_exec("ping -c 1 -W 1 $ip 2>/dev/null");
            }

            if ($ping_result && strpos($ping_result, 'TTL') !== false) {
                // تحديث جدول ARP والبحث مرة أخرى
                $found_ip = findIpFromMac_arp_table($mac_address);
                if ($found_ip) {
                    return $found_ip;
                }
            }
        }
    } catch (Exception $e) {
        logError("Error in ping sweep: " . $e->getMessage());
    }

    return null;
}

// البحث عبر nmap (إذا كان متوفر)
function findIpFromMac_nmap_scan($mac_address) {
    try {
        $local_ip = getLocalIP();
        if (!$local_ip) {
            return null;
        }

        $network = substr($local_ip, 0, strrpos($local_ip, '.')) . '.0/24';

        // تشغيل nmap إذا كان متوفر
        $nmap_output = shell_exec("nmap -sn $network 2>/dev/null");
        if ($nmap_output) {
            // تحليل نتائج nmap وتحديث ARP
            $found_ip = findIpFromMac_arp_table($mac_address);
            if ($found_ip) {
                return $found_ip;
            }
        }
    } catch (Exception $e) {
        logError("Error in nmap scan: " . $e->getMessage());
    }

    return null;
}

// البحث عبر neighbor discovery
function findIpFromMac_neighbor_discovery($mac_address) {
    try {
        // IPv6 neighbor discovery (Linux)
        if (strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
            $output = shell_exec('ip neigh show 2>/dev/null');
            if ($output) {
                $lines = explode("\n", $output);
                foreach ($lines as $line) {
                    if (preg_match('/(\d+\.\d+\.\d+\.\d+).*lladdr ([a-f0-9:]{17})/i', $line, $matches)) {
                        $found_mac = strtolower(str_replace(':', '', $matches[2]));
                        if ($found_mac === $mac_address) {
                            return $matches[1];
                        }
                    }
                }
            }
        }
    } catch (Exception $e) {
        logError("Error in neighbor discovery: " . $e->getMessage());
    }

    return null;
}

// دالة للحصول على IP المحلي
function getLocalIP() {
    try {
        // محاولة الحصول على IP المحلي
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $output = shell_exec('ipconfig');
            if (preg_match('/IPv4 Address[.\s]*:\s*(\d+\.\d+\.\d+\.\d+)/', $output, $matches)) {
                return $matches[1];
            }
        } else {
            $output = shell_exec("ip route get ******* | awk '{print $7}' | head -1");
            if ($output && filter_var(trim($output), FILTER_VALIDATE_IP)) {
                return trim($output);
            }
        }

        // طريقة بديلة
        $sock = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        socket_connect($sock, "*******", 53);
        socket_getsockname($sock, $name);
        socket_close($sock);

        return $name;
    } catch (Exception $e) {
        logError("Error getting local IP: " . $e->getMessage());
        return '*************'; // IP افتراضي
    }
}

// دالة لتنسيق MAC Address
function formatMacAddress($mac, $separator = ':') {
    $mac = strtolower(preg_replace('/[^a-f0-9]/', '', $mac));
    if (strlen($mac) !== 12) {
        return false;
    }

    return implode($separator, str_split($mac, 2));
}

// دالة للتحقق من صحة MAC Address
function isValidMacAddress($mac) {
    if (empty($mac)) {
        return false;
    }

    // إزالة جميع الفواصل والمسافات
    $clean_mac = strtolower(preg_replace('/[^a-f0-9]/', '', $mac));

    // التحقق من الطول (يجب أن يكون 12 حرف) وأن جميع الأحرف hex
    return strlen($clean_mac) === 12 && ctype_xdigit($clean_mac);
}
?>
