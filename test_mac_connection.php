<?php
/**
 * اختبار الاتصال بـ MikroTik عبر MAC Address
 */

session_start();
require_once 'config.php';
require_once 'functions.php';

// معالجة اختبار الاتصال
$test_result = null;
if ($_POST['action'] == 'test_mac_connection') {
    $mac_address = validateInput($_POST['mac_address']);
    
    if (isValidMacAddress($mac_address)) {
        $mac_address = formatMacAddress($mac_address, ':');
        $test_result = testMacConnection($mac_address);
    } else {
        $test_result = [
            'success' => false,
            'message' => 'MAC Address غير صحيح'
        ];
    }
}

// دالة اختبار الاتصال بـ MAC
function testMacConnection($mac_address) {
    $result = [
        'success' => false,
        'message' => '',
        'details' => [],
        'ip_found' => null,
        'connection_test' => false
    ];
    
    try {
        // البحث عن IP من MAC
        $ip_address = findIpFromMac($mac_address);
        
        if ($ip_address) {
            $result['ip_found'] = $ip_address;
            $result['details'][] = "تم العثور على IP: $ip_address";
            
            // اختبار الاتصال بـ MikroTik
            if (file_exists('mikrotik_api.php')) {
                require_once 'mikrotik_api.php';
                $api = new RouterosAPI();
                $api->debug = false;
                
                if ($api->connect($ip_address, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
                    $result['connection_test'] = true;
                    $result['success'] = true;
                    $result['message'] = 'تم الاتصال بنجاح عبر MAC Address';
                    
                    // الحصول على معلومات النظام
                    $system_info = $api->comm('/system/identity/print');
                    if (!empty($system_info)) {
                        $result['details'][] = "اسم النظام: " . $system_info[0]['name'];
                    }
                    
                    $api->disconnect();
                } else {
                    $result['message'] = 'تم العثور على IP لكن فشل الاتصال بـ MikroTik';
                    $result['details'][] = 'تحقق من بيانات المستخدم وكلمة المرور';
                }
            } else {
                $result['message'] = 'تم العثور على IP لكن ملف MikroTik API غير موجود';
            }
        } else {
            $result['message'] = 'لم يتم العثور على IP للـ MAC Address المحدد';
            $result['details'][] = 'تأكد من أن الجهاز متصل بالشبكة';
            $result['details'][] = 'جرب ping الجهاز أولاً لتحديث جدول ARP';
        }
        
    } catch (Exception $e) {
        $result['message'] = 'خطأ في الاختبار: ' . $e->getMessage();
    }
    
    return $result;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بـ MAC Address - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔗 اختبار الاتصال بـ MAC Address</h1>
        
        <!-- نموذج الاختبار -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-network-wired"></i> اختبار الاتصال</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="test_mac_connection">
                            
                            <div class="mb-3">
                                <label class="form-label">MAC Address</label>
                                <div class="input-group">
                                    <input type="text" name="mac_address" class="form-control" 
                                           placeholder="AA:BB:CC:DD:EE:FF" 
                                           value="<?= isset($_POST['mac_address']) ? htmlspecialchars($_POST['mac_address']) : '' ?>"
                                           required>
                                    <button type="button" class="btn btn-outline-secondary" onclick="detectDevices()">
                                        <i class="fas fa-search"></i> اكتشاف
                                    </button>
                                </div>
                                <div class="form-text">أدخل MAC Address الخاص بجهاز MikroTik</div>
                            </div>
                            
                            <div id="detection_results" class="mb-3"></div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plug"></i> اختبار الاتصال
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نتائج الاختبار -->
        <?php if ($test_result): ?>
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header <?= $test_result['success'] ? 'bg-success' : 'bg-danger' ?> text-white">
                        <h5>
                            <i class="fas <?= $test_result['success'] ? 'fa-check-circle' : 'fa-times-circle' ?>"></i>
                            نتيجة الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert <?= $test_result['success'] ? 'alert-success' : 'alert-danger' ?>">
                            <strong><?= htmlspecialchars($test_result['message']) ?></strong>
                        </div>
                        
                        <?php if (!empty($test_result['details'])): ?>
                        <h6>التفاصيل:</h6>
                        <ul class="list-unstyled">
                            <?php foreach ($test_result['details'] as $detail): ?>
                                <li><i class="fas fa-info-circle text-info"></i> <?= htmlspecialchars($detail) ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6>معلومات الاختبار:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>MAC Address:</strong> <?= htmlspecialchars($_POST['mac_address']) ?></li>
                                    <li><strong>IP المكتشف:</strong> 
                                        <?php if ($test_result['ip_found']): ?>
                                            <span class="badge bg-success"><?= $test_result['ip_found'] ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير موجود</span>
                                        <?php endif; ?>
                                    </li>
                                    <li><strong>اختبار الاتصال:</strong>
                                        <?php if ($test_result['connection_test']): ?>
                                            <span class="badge bg-success">نجح</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">فشل</span>
                                        <?php endif; ?>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>الخطوات التالية:</h6>
                                <?php if ($test_result['success']): ?>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> يمكنك استخدام هذا MAC في الإعدادات</li>
                                        <li><i class="fas fa-arrow-right text-info"></i> <a href="settings.php">انتقل للإعدادات</a></li>
                                    </ul>
                                <?php else: ?>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-exclamation-triangle text-warning"></i> تحقق من تشغيل الجهاز</li>
                                        <li><i class="fas fa-network-wired text-info"></i> تأكد من الاتصال بنفس الشبكة</li>
                                        <li><i class="fas fa-sync text-secondary"></i> جرب ping الجهاز أولاً</li>
                                    </ul>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- معلومات إضافية -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6><i class="fas fa-info-circle"></i> معلومات مفيدة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>كيفية العثور على MAC Address:</h6>
                                <ul class="list-unstyled">
                                    <li>• من واجهة MikroTik: <code>/interface print</code></li>
                                    <li>• من الملصق على الجهاز</li>
                                    <li>• من إعدادات الشبكة في الراوتر</li>
                                    <li>• استخدام أمر <code>arp -a</code></li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>مميزات الاتصال بـ MAC:</h6>
                                <ul class="list-unstyled">
                                    <li>• لا يتأثر بتغيير IP</li>
                                    <li>• مفيد في الشبكات الديناميكية</li>
                                    <li>• يعمل مع DHCP</li>
                                    <li>• أكثر مرونة في الإعداد</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>أمثلة على تنسيقات MAC Address المقبولة:</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <code>AA:BB:CC:DD:EE:FF</code>
                                </div>
                                <div class="col-md-4">
                                    <code>AA-BB-CC-DD-EE-FF</code>
                                </div>
                                <div class="col-md-4">
                                    <code>AABBCCDDEEFF</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="text-center">
                    <a href="settings.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                    <a href="test_sync.php" class="btn btn-outline-info me-2">
                        <i class="fas fa-sync"></i> اختبار المزامنة
                    </a>
                    <a href="index.php" class="btn btn-outline-success">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">تم إنشاء هذا التقرير في: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function detectDevices() {
            const resultDiv = document.getElementById('detection_results');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> جاري الاتصال بسيرفر MikroTik واكتشاف الأجهزة...';

            fetch('detect_mikrotik_direct.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.devices.length > 0) {
                        let html = '<div class="alert alert-success">';
                        html += '<h6><i class="fas fa-server"></i> تم الاتصال بسيرفر MikroTik!</h6>';

                        if (data.system_info && data.system_info.identity) {
                            html += '<p><strong>اسم الراوتر:</strong> ' + data.system_info.identity + '</p>';
                        }

                        html += '<h6>الأجهزة المتصلة (' + data.count + ' جهاز):</h6>';
                        html += '<div style="max-height: 250px; overflow-y: auto;"><ul class="list-unstyled">';

                        data.devices.forEach(device => {
                            let deviceType = '';
                            if (device.type === 'Router Interface') {
                                deviceType = '<span class="badge bg-primary">راوتر</span>';
                            } else if (device.type === 'DHCP Client') {
                                deviceType = '<span class="badge bg-success">DHCP</span>';
                            } else if (device.type === 'Wireless Client') {
                                deviceType = '<span class="badge bg-info">لاسلكي</span>';
                            } else {
                                deviceType = '<span class="badge bg-secondary">متصل</span>';
                            }

                            html += `<li class="mb-2">
                                <button type="button" class="btn btn-sm btn-outline-primary me-2"
                                        onclick="selectMac('${device.mac}')">
                                    اختيار
                                </button>
                                <strong>${device.mac}</strong>
                                ${device.ip ? ' - ' + device.ip : ''}
                                ${device.hostname ? ' (' + device.hostname + ')' : ''}
                                ${deviceType}
                            </li>`;
                        });

                        html += '</ul></div></div>';
                        resultDiv.innerHTML = html;
                    } else {
                        let errorMsg = data.error || 'لم يتم العثور على أجهزة متصلة';
                        resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> ' + errorMsg + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle"></i> خطأ في الاتصال: ' + error.message + '</div>';
                });
        }
        
        function selectMac(mac) {
            document.querySelector('input[name="mac_address"]').value = mac;
            document.getElementById('detection_results').innerHTML = 
                '<div class="alert alert-info">تم اختيار: <strong>' + mac + '</strong></div>';
        }
    </script>
</body>
</html>
