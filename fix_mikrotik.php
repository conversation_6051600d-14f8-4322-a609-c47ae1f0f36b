<?php
/**
 * إصلاح وتشخيص مشاكل الاتصال بـ MikroTik
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح الاتصال بـ MikroTik</h1>";

// قراءة الإعدادات الحالية
if (file_exists('config.php')) {
    require_once 'config.php';
    
    echo "<h2>الإعدادات الحالية:</h2>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . (defined('MIKROTIK_HOST') ? MIKROTIK_HOST : 'غير محدد') . "</li>";
    echo "<li><strong>User:</strong> " . (defined('MIKROTIK_USER') ? MIKROTIK_USER : 'غير محدد') . "</li>";
    echo "<li><strong>Port:</strong> " . (defined('MIKROTIK_PORT') ? MIKROTIK_PORT : 'غير محدد') . "</li>";
    echo "<li><strong>Password:</strong> " . (defined('MIKROTIK_PASS') && !empty(MIKROTIK_PASS) ? '***' : 'فارغ') . "</li>";
    echo "</ul>";
    
    // نموذج لتحديث الإعدادات
    if ($_POST) {
        echo "<h2>تحديث الإعدادات:</h2>";
        
        $new_host = $_POST['mikrotik_host'] ?? MIKROTIK_HOST;
        $new_user = $_POST['mikrotik_user'] ?? MIKROTIK_USER;
        $new_pass = $_POST['mikrotik_pass'] ?? MIKROTIK_PASS;
        $new_port = $_POST['mikrotik_port'] ?? MIKROTIK_PORT;
        
        // قراءة ملف config.php
        $config_content = file_get_contents('config.php');
        
        // تحديث القيم
        $config_content = preg_replace("/define\('MIKROTIK_HOST', '[^']*'\);/", "define('MIKROTIK_HOST', '$new_host');", $config_content);
        $config_content = preg_replace("/define\('MIKROTIK_USER', '[^']*'\);/", "define('MIKROTIK_USER', '$new_user');", $config_content);
        $config_content = preg_replace("/define\('MIKROTIK_PASS', '[^']*'\);/", "define('MIKROTIK_PASS', '$new_pass');", $config_content);
        $config_content = preg_replace("/define\('MIKROTIK_PORT', [^)]*\);/", "define('MIKROTIK_PORT', $new_port);", $config_content);
        
        // حفظ الملف
        if (file_put_contents('config.php', $config_content)) {
            echo "<div style='color: green;'>✅ تم تحديث الإعدادات بنجاح</div>";
            echo "<div>يرجى إعادة تحميل الصفحة لاختبار الإعدادات الجديدة.</div>";
            echo "<a href='fix_mikrotik.php' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>إعادة تحميل</a>";
        } else {
            echo "<div style='color: red;'>❌ فشل في تحديث الإعدادات</div>";
        }
    } else {
        // اختبار الاتصال
        echo "<h2>اختبار الاتصال:</h2>";
        
        if (file_exists('mikrotik_api.php')) {
            require_once 'mikrotik_api.php';
            
            try {
                $api = new RouterosAPI();
                $api->debug = false;
                
                echo "<div>محاولة الاتصال بـ: " . MIKROTIK_HOST . ":" . MIKROTIK_PORT . "</div>";
                
                if ($api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
                    echo "<div style='color: green; font-size: 18px;'>✅ تم الاتصال بـ MikroTik بنجاح!</div>";
                    
                    // اختبار بعض الأوامر
                    try {
                        $identity = $api->comm('/system/identity/print');
                        if (!empty($identity)) {
                            echo "<div><strong>اسم الراوتر:</strong> " . $identity[0]['name'] . "</div>";
                        }
                        
                        $resource = $api->comm('/system/resource/print');
                        if (!empty($resource)) {
                            echo "<div><strong>إصدار RouterOS:</strong> " . $resource[0]['version'] . "</div>";
                            echo "<div><strong>نوع الجهاز:</strong> " . $resource[0]['board-name'] . "</div>";
                        }
                        
                        // فحص خدمة API
                        $api_service = $api->comm('/ip/service/print', array('?name' => 'api'));
                        if (!empty($api_service)) {
                            $api_status = $api_service[0]['disabled'] ?? 'false';
                            if ($api_status === 'false') {
                                echo "<div style='color: green;'>✅ خدمة API مفعلة</div>";
                            } else {
                                echo "<div style='color: red;'>❌ خدمة API معطلة</div>";
                            }
                        }
                        
                    } catch (Exception $e) {
                        echo "<div style='color: orange;'>⚠️ تم الاتصال لكن فشل في تنفيذ بعض الأوامر: " . $e->getMessage() . "</div>";
                    }
                    
                    $api->disconnect();
                    
                    echo "<hr>";
                    echo "<div style='color: green; font-size: 18px; font-weight: bold;'>🎉 الاتصال يعمل بشكل صحيح!</div>";
                    echo "<div>يمكنك الآن العودة إلى النظام واستخدامه بشكل طبيعي.</div>";
                    
                } else {
                    echo "<div style='color: red; font-size: 16px;'>❌ فشل الاتصال بـ MikroTik</div>";
                    
                    echo "<h3>الأسباب المحتملة والحلول:</h3>";
                    echo "<ol>";
                    echo "<li><strong>عنوان IP خاطئ:</strong> تأكد من أن عنوان IP صحيح ومتاح</li>";
                    echo "<li><strong>خدمة API معطلة:</strong> فعل خدمة API في MikroTik</li>";
                    echo "<li><strong>اسم المستخدم أو كلمة المرور خاطئة:</strong> تحقق من بيانات الدخول</li>";
                    echo "<li><strong>البورت مغلق:</strong> تأكد من أن البورت 8728 مفتوح</li>";
                    echo "<li><strong>جدار الحماية:</strong> تأكد من أن جدار الحماية لا يحجب الاتصال</li>";
                    echo "</ol>";
                    
                    echo "<h3>تحديث الإعدادات:</h3>";
                    echo "<form method='post' style='background: #f9f9f9; padding: 20px; border-radius: 5px;'>";
                    echo "<table>";
                    echo "<tr><td><label>عنوان IP:</label></td><td><input type='text' name='mikrotik_host' value='" . MIKROTIK_HOST . "' style='width: 200px; padding: 5px;'></td></tr>";
                    echo "<tr><td><label>اسم المستخدم:</label></td><td><input type='text' name='mikrotik_user' value='" . MIKROTIK_USER . "' style='width: 200px; padding: 5px;'></td></tr>";
                    echo "<tr><td><label>كلمة المرور:</label></td><td><input type='password' name='mikrotik_pass' value='" . MIKROTIK_PASS . "' style='width: 200px; padding: 5px;'></td></tr>";
                    echo "<tr><td><label>البورت:</label></td><td><input type='number' name='mikrotik_port' value='" . MIKROTIK_PORT . "' style='width: 200px; padding: 5px;'></td></tr>";
                    echo "<tr><td colspan='2'><input type='submit' value='تحديث وإعادة الاختبار' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'></td></tr>";
                    echo "</table>";
                    echo "</form>";
                }
                
            } catch (Exception $e) {
                echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div style='color: red;'>❌ ملف mikrotik_api.php غير موجود</div>";
        }
    }
} else {
    echo "<div style='color: red;'>❌ ملف config.php غير موجود</div>";
    echo "<div>يرجى تشغيل install.php أولاً لإعداد النظام.</div>";
}

echo "<hr>";
echo "<h2>روابط مفيدة:</h2>";
echo "<ul>";
echo "<li><a href='test_connection.php'>اختبار شامل للاتصالات</a></li>";
echo "<li><a href='fix_database.php'>إصلاح قاعدة البيانات</a></li>";
echo "<li><a href='index.php'>العودة إلى النظام الرئيسي</a></li>";
echo "</ul>";
?>
