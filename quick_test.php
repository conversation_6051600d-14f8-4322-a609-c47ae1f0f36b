<?php
// اختبار سريع للنظام
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 اختبار سريع للنظام</h1>";

echo "<h3>1. اختبار الملفات الأساسية:</h3>";

$files = ['config.php', 'functions.php', 'index.php', 'settings.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

echo "<h3>2. اختبار تحميل الملفات:</h3>";

try {
    require_once 'config.php';
    echo "✅ config.php تم تحميله<br>";
} catch (Exception $e) {
    echo "❌ خطأ في config.php: " . $e->getMessage() . "<br>";
}

try {
    require_once 'functions.php';
    echo "✅ functions.php تم تحميله<br>";
} catch (Exception $e) {
    echo "❌ خطأ في functions.php: " . $e->getMessage() . "<br>";
}

echo "<h3>3. اختبار قاعدة البيانات:</h3>";

if (isset($conn) && $conn) {
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // اختبار الجداول
    $tables = ['hotspot_cards', 'hotspot_profiles', 'settings'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "❌ جدول $table غير موجود<br>";
        }
    }
} else {
    echo "❌ فشل الاتصال بقاعدة البيانات<br>";
}

echo "<h3>4. اختبار الدوال:</h3>";

$functions = ['generateUsername', 'generatePassword', 'validateInput'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✅ دالة $func موجودة<br>";
    } else {
        echo "❌ دالة $func غير موجودة<br>";
    }
}

echo "<h3>5. اختبار ملف الاكتشاف:</h3>";

try {
    $url = 'detect_mikrotik_simple.php';
    $response = file_get_contents($url);
    $data = json_decode($response, true);
    
    if ($data && isset($data['success'])) {
        echo "✅ ملف الاكتشاف يعمل<br>";
        echo "📊 عدد الأجهزة المكتشفة: " . $data['count'] . "<br>";
    } else {
        echo "❌ ملف الاكتشاف لا يعمل<br>";
        echo "📄 الاستجابة: " . htmlspecialchars($response) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار الاكتشاف: " . $e->getMessage() . "<br>";
}

echo "<h3>6. اختبار الاكتشاف المباشر:</h3>";

try {
    $url = 'detect_mikrotik_direct.php';
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data && isset($data['success'])) {
        if ($data['success']) {
            echo "✅ الاكتشاف المباشر يعمل<br>";
            echo "📊 عدد الأجهزة المكتشفة: " . $data['count'] . "<br>";
            if (isset($data['system_info']['identity'])) {
                echo "🏷️ اسم الراوتر: " . $data['system_info']['identity'] . "<br>";
            }
        } else {
            echo "❌ فشل الاكتشاف المباشر: " . ($data['error'] ?? 'خطأ غير معروف') . "<br>";
        }
    } else {
        echo "❌ استجابة غير صحيحة من ملف الاكتشاف المباشر<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار الاكتشاف المباشر: " . $e->getMessage() . "<br>";
}

echo "<h3>7. روابط الاختبار:</h3>";
echo "<a href='quick_setup.php' class='btn' style='background: #28a745;'>🚀 الإعداد السريع</a><br>";
echo "<a href='index.php' class='btn'>🏠 الصفحة الرئيسية</a><br>";
echo "<a href='index_simple.php' class='btn'>🔧 النسخة المبسطة</a><br>";
echo "<a href='settings.php' class='btn'>⚙️ الإعدادات</a><br>";
echo "<a href='debug_connection.php' class='btn' style='background: #ffc107;'>🔧 تشخيص الاتصال</a><br>";
echo "<a href='test_direct_detection.php' class='btn'>🔍 اختبار الاكتشاف المباشر</a><br>";

echo "<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    margin: 5px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
}
.btn:hover { background: #0056b3; }
</style>";

echo "<h3>8. النتيجة:</h3>";
echo "<div style='padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "🎉 إذا رأيت معظم العلامات الخضراء أعلاه، فالنظام يعمل بشكل صحيح!<br>";
echo "جرب الروابط أعلاه للتأكد من عمل جميع الصفحات.";
echo "</div>";
?>
