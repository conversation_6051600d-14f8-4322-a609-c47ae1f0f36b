<?php
/**
 * ملف اختبار النظام
 * للتحقق من حالة قاعدة البيانات والإعدادات
 */

// تفعيل عرض الأخطاء للاختبار
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار نظام MikroSys</h2>";
echo "<hr>";

// اختبار 1: التحقق من إصدار PHP
echo "<h3>1. إصدار PHP</h3>";
echo "الإصدار الحالي: " . PHP_VERSION;
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    echo " ✅ مدعوم<br>";
} else {
    echo " ❌ غير مدعوم (يتطلب 7.4+)<br>";
}

// اختبار 2: التحقق من الامتدادات المطلوبة
echo "<h3>2. امتدادات PHP المطلوبة</h3>";
$required_extensions = ['mysqli', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    echo "امتداد $ext: ";
    if (extension_loaded($ext)) {
        echo "✅ متوفر<br>";
    } else {
        echo "❌ غير متوفر<br>";
    }
}

// اختبار 3: التحقق من ملف الإعدادات
echo "<h3>3. ملف الإعدادات</h3>";
if (file_exists('config.php')) {
    echo "ملف config.php: ✅ موجود<br>";
    
    // محاولة تضمين الملف
    try {
        require_once 'config.php';
        echo "تحميل الإعدادات: ✅ نجح<br>";
        
        // عرض إعدادات قاعدة البيانات
        echo "خادم قاعدة البيانات: " . DB_HOST . "<br>";
        echo "اسم المستخدم: " . DB_USER . "<br>";
        echo "اسم قاعدة البيانات: " . DB_NAME . "<br>";
        
    } catch (Exception $e) {
        echo "تحميل الإعدادات: ❌ فشل - " . $e->getMessage() . "<br>";
    }
} else {
    echo "ملف config.php: ❌ غير موجود<br>";
}

// اختبار 4: التحقق من الاتصال بقاعدة البيانات
echo "<h3>4. اتصال قاعدة البيانات</h3>";
if (isset($conn) && $conn) {
    echo "الاتصال بقاعدة البيانات: ✅ نجح<br>";
    echo "إصدار MySQL: " . $conn->server_info . "<br>";
    
    // اختبار الجداول
    $tables = ['hotspot_cards', 'hotspot_profiles', 'system_settings'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "جدول $table: ✅ موجود<br>";
        } else {
            echo "جدول $table: ❌ غير موجود<br>";
        }
    }
    
    // عدد البروفايلات
    $result = $conn->query("SELECT COUNT(*) as count FROM hotspot_profiles");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "عدد البروفايلات: " . $row['count'] . "<br>";
    }
    
    // عدد الكروت
    $result = $conn->query("SELECT COUNT(*) as count FROM hotspot_cards");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "عدد الكروت: " . $row['count'] . "<br>";
    }
    
} else {
    echo "الاتصال بقاعدة البيانات: ❌ فشل<br>";
    
    // محاولة تشخيص المشكلة
    echo "<h4>تشخيص مشكلة قاعدة البيانات:</h4>";
    
    try {
        $test_conn = new mysqli(DB_HOST, DB_USER, DB_PASS);
        if ($test_conn->connect_error) {
            echo "خطأ في الاتصال: " . $test_conn->connect_error . "<br>";
            echo "تحقق من:<br>";
            echo "- صحة اسم المستخدم وكلمة المرور<br>";
            echo "- تشغيل خادم MySQL<br>";
            echo "- صلاحيات المستخدم<br>";
        } else {
            echo "الاتصال بالخادم: ✅ نجح<br>";
            
            // التحقق من وجود قاعدة البيانات
            $result = $test_conn->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
            if ($result && $result->num_rows > 0) {
                echo "قاعدة البيانات '" . DB_NAME . "': ✅ موجودة<br>";
            } else {
                echo "قاعدة البيانات '" . DB_NAME . "': ❌ غير موجودة<br>";
                echo "سيتم إنشاؤها تلقائياً عند أول تشغيل<br>";
            }
            $test_conn->close();
        }
    } catch (Exception $e) {
        echo "خطأ في الاختبار: " . $e->getMessage() . "<br>";
    }
}

// اختبار 5: التحقق من الملفات المطلوبة
echo "<h3>5. الملفات المطلوبة</h3>";
$required_files = [
    'index.php' => 'الصفحة الرئيسية',
    'functions.php' => 'ملف الدوال',
    'mikrotik_api.php' => 'مكتبة MikroTik API',
    'print_cards.php' => 'صفحة طباعة الكروت',
    'settings.php' => 'صفحة الإعدادات'
];

foreach ($required_files as $file => $description) {
    echo "$description ($file): ";
    if (file_exists($file)) {
        echo "✅ موجود<br>";
    } else {
        echo "❌ غير موجود<br>";
    }
}

// اختبار 6: التحقق من المجلدات والصلاحيات
echo "<h3>6. المجلدات والصلاحيات</h3>";

// مجلد اللوجات
echo "مجلد logs: ";
if (file_exists('logs')) {
    echo "✅ موجود";
    if (is_writable('logs')) {
        echo " (قابل للكتابة)<br>";
    } else {
        echo " ❌ (غير قابل للكتابة)<br>";
    }
} else {
    echo "❌ غير موجود<br>";
    if (mkdir('logs', 0755, true)) {
        echo "تم إنشاء مجلد logs: ✅<br>";
    } else {
        echo "فشل إنشاء مجلد logs: ❌<br>";
    }
}

// صلاحيات الكتابة في المجلد الحالي
echo "صلاحيات الكتابة: ";
if (is_writable('.')) {
    echo "✅ متوفرة<br>";
} else {
    echo "❌ غير متوفرة<br>";
}

// اختبار 7: اختبار MikroTik (اختياري)
echo "<h3>7. اتصال MikroTik (اختياري)</h3>";
if (function_exists('checkMikroTikConnection')) {
    if (checkMikroTikConnection()) {
        echo "الاتصال بـ MikroTik: ✅ نجح<br>";
    } else {
        echo "الاتصال بـ MikroTik: ❌ فشل<br>";
        echo "تحقق من إعدادات MikroTik في config.php<br>";
    }
} else {
    echo "دالة اختبار MikroTik: ❌ غير متوفرة<br>";
}

// اختبار 8: معلومات النظام
echo "<h3>8. معلومات النظام</h3>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "خادم الويب: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "<br>";
echo "استخدام الذاكرة: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB<br>";
echo "الحد الأقصى للذاكرة: " . ini_get('memory_limit') . "<br>";
echo "الحد الأقصى لوقت التنفيذ: " . ini_get('max_execution_time') . " ثانية<br>";

// خلاصة الاختبار
echo "<hr>";
echo "<h3>خلاصة الاختبار</h3>";

$issues = [];

if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    $issues[] = "إصدار PHP قديم";
}

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $issues[] = "امتداد $ext غير متوفر";
    }
}

if (!file_exists('config.php')) {
    $issues[] = "ملف الإعدادات غير موجود";
}

if (!isset($conn) || !$conn) {
    $issues[] = "مشكلة في اتصال قاعدة البيانات";
}

if (empty($issues)) {
    echo "<div style='color: green; font-weight: bold;'>✅ جميع الاختبارات نجحت! النظام جاهز للاستخدام.</div>";
    echo "<p><a href='index.php'>انتقل إلى النظام</a></p>";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ توجد مشاكل تحتاج إلى حل:</div>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "<p>يرجى حل هذه المشاكل قبل استخدام النظام.</p>";
}

echo "<hr>";
echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>
