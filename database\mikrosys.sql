-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 06, 2025 at 04:47 PM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `mikrosys`
--

-- --------------------------------------------------------

--
-- Table structure for table `hotspot_cards`
--

CREATE TABLE `hotspot_cards` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(50) NOT NULL,
  `profile` varchar(50) NOT NULL,
  `status` enum('active','used','expired') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `mikrotik_id` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `hotspot_profiles`
--

CREATE TABLE `hotspot_profiles` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `session_timeout` int(11) DEFAULT 0,
  `idle_timeout` int(11) DEFAULT 0,
  `rate_limit` varchar(50) DEFAULT '',
  `shared_users` int(11) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `hotspot_profiles`
--

INSERT INTO `hotspot_profiles` (`id`, `name`, `display_name`, `session_timeout`, `idle_timeout`, `rate_limit`, `shared_users`, `created_at`) VALUES
(1, '1hour', 'ساعة واحدة', 3600, 300, '1M/1M', 1, '2025-08-06 12:55:12'),
(2, '3hours', '3 ساعات', 10800, 300, '2M/2M', 1, '2025-08-06 12:55:12'),
(3, '6hours', '6 ساعات', 21600, 600, '3M/3M', 1, '2025-08-06 12:55:12'),
(4, '12hours', '12 ساعة', 43200, 600, '5M/5M', 1, '2025-08-06 12:55:12'),
(5, '24hours', '24 ساعة', 86400, 900, '10M/10M', 1, '2025-08-06 12:55:12'),
(6, '7days', 'أسبوع', 604800, 1800, '15M/15M', 2, '2025-08-06 12:55:12'),
(7, '30days', 'شهر', 2592000, 3600, '20M/20M', 3, '2025-08-06 12:55:12');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `updated_at`) VALUES
(1, 'mikrotik_host', '**********', '2025-08-06 14:39:35'),
(2, 'mikrotik_mac', '', '2025-08-06 14:39:35'),
(3, 'connection_type', 'ip', '2025-08-06 14:39:35'),
(4, 'mikrotik_user', 'mohager', '2025-08-06 14:39:35'),
(5, 'mikrotik_pass', 'P@$$w0rd', '2025-08-06 14:39:35'),
(6, 'mikrotik_port', '8728', '2025-08-06 14:39:35');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `hotspot_cards`
--
ALTER TABLE `hotspot_cards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `hotspot_profiles`
--
ALTER TABLE `hotspot_profiles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `hotspot_cards`
--
ALTER TABLE `hotspot_cards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `hotspot_profiles`
--
ALTER TABLE `hotspot_profiles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=834;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
