/* ملف CSS خاص بطباعة الكروت */

/* إعدادات الطباعة العامة */
@media print {
    @page {
        size: A4;
        margin: 5mm;
    }
    
    body {
        margin: 0;
        padding: 0;
        background: white !important;
        font-family: Arial, sans-serif;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-container {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        background: white !important;
    }
    
    .page-break {
        page-break-after: always;
    }
    
    .page-info {
        display: none;
    }
}

/* تحسينات خاصة للكروت الصغيرة */
.card-compact {
    width: 65mm;
    height: 40mm;
    border: 1px solid #333;
    border-radius: 4px;
    margin: 1mm;
    padding: 1.5mm;
    display: inline-block;
    vertical-align: top;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
    page-break-inside: avoid;
    font-size: 8px;
}

.card-compact .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 0.5px solid rgba(255,255,255,0.3);
    padding-bottom: 0.5mm;
    margin-bottom: 1mm;
}

.card-compact .card-logo {
    height: 12px;
    width: auto;
    max-width: 20px;
    object-fit: contain;
    opacity: 0.9;
}

.card-compact .card-title {
    font-size: 8px;
    font-weight: bold;
    margin: 0;
    text-align: center;
    flex: 1;
}

.card-compact .card-subtitle {
    font-size: 5px;
    margin: 0;
    opacity: 0.8;
    text-align: center;
}

.card-compact .wifi-icon {
    font-size: 10px;
    opacity: 0.7;
}

.card-compact .credential {
    margin: 0.5mm 0;
    text-align: center;
}

.card-compact .credential-label {
    font-size: 5px;
    opacity: 0.8;
    margin-bottom: 0.2px;
}

.card-compact .credential-value {
    font-size: 9px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    background: rgba(255,255,255,0.2);
    padding: 0.5px 2px;
    border-radius: 1px;
    letter-spacing: 0.3px;
}

.card-compact .card-footer {
    text-align: center;
    border-top: 0.5px solid rgba(255,255,255,0.3);
    padding-top: 0.5mm;
    font-size: 4px;
    opacity: 0.8;
    line-height: 1.1;
}

/* تخطيط محسن للكروت */
.cards-grid-compact {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 0.5mm;
    margin: 0;
    padding: 0;
}

/* تحسينات للطباعة بالأبيض والأسود */
@media print and (monochrome) {
    .card, .card-compact {
        background: white !important;
        color: black !important;
        border: 2px solid black !important;
    }
    
    .credential-value {
        background: #f0f0f0 !important;
        color: black !important;
    }
    
    .card-header {
        border-bottom-color: black !important;
    }
    
    .card-footer {
        border-top-color: black !important;
    }
}

/* تحسينات للطباعة الاقتصادية */
.eco-print .card {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
}

.eco-print .credential-value {
    background: #f5f5f5 !important;
    color: black !important;
    border: 1px solid #ccc !important;
}

.eco-print .card-header,
.eco-print .card-footer {
    border-color: black !important;
}

/* تخطيط مرن للكروت */
.cards-per-row-3 .card { width: 30%; margin: 1%; }
.cards-per-row-4 .card { width: 23%; margin: 1%; }
.cards-per-row-5 .card { width: 18%; margin: 1%; }

/* تحسينات للشاشة */
@media screen {
    .print-preview {
        background: #f5f5f5;
        padding: 20px;
    }
    
    .print-preview .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
}

/* أنماط خاصة للوجو في الطباعة */
@media print {
    .card-logo, .system-logo {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* تحسينات للطباعة عالية الجودة */
.high-quality-print .card {
    border-width: 1.5px;
    border-style: solid;
}

.high-quality-print .card-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* تخطيط خاص للكروت المصغرة جداً */
.mini-cards .card {
    width: 50mm;
    height: 32mm;
    font-size: 6px;
    padding: 1mm;
}

.mini-cards .card-logo {
    height: 8px;
}

.mini-cards .credential-value {
    font-size: 7px;
}

.mini-cards .card-footer {
    font-size: 3px;
}

/* تحسينات لتوفير الحبر */
.ink-saver .card {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
}

.ink-saver .card::before {
    display: none !important;
}

.ink-saver .credential-value {
    background: transparent !important;
    border: 1px dashed black !important;
    color: black !important;
}
