#!/bin/bash

# سكريبت تنظيف تلقائي لنظام MikroSys
# يتم تشغيله عبر Cron Job لتنظيف الكروت المنتهية الصلاحية

# مسار مجلد النظام
SYSTEM_PATH="/path/to/mikrosys"

# مسار PHP
PHP_PATH="/usr/bin/php"

# مسار ملف السجل
LOG_FILE="$SYSTEM_PATH/logs/cron_cleanup.log"

# التاريخ والوقت الحالي
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# إنشاء مجلد اللوجات إذا لم يكن موجوداً
mkdir -p "$SYSTEM_PATH/logs"

# كتابة بداية العملية في السجل
echo "[$TIMESTAMP] بدء عملية التنظيف التلقائي" >> "$LOG_FILE"

# الانتقال لمجلد النظام
cd "$SYSTEM_PATH"

# تشغيل سكريبت التنظيف
if [ -f "cleanup.php" ]; then
    # تشغيل التنظيف العادي
    OUTPUT=$($PHP_PATH cleanup.php 2>&1)
    echo "[$TIMESTAMP] نتيجة التنظيف العادي: $OUTPUT" >> "$LOG_FILE"
    
    # تشغيل التنظيف الشامل (حذف الكروت القديمة) مرة واحدة أسبوعياً
    DAY_OF_WEEK=$(date '+%u')  # 1=الاثنين, 7=الأحد
    if [ "$DAY_OF_WEEK" -eq 1 ]; then  # يوم الاثنين
        echo "[$TIMESTAMP] تشغيل التنظيف الشامل الأسبوعي" >> "$LOG_FILE"
        DEEP_OUTPUT=$($PHP_PATH cleanup.php?cleanup_old=1 2>&1)
        echo "[$TIMESTAMP] نتيجة التنظيف الشامل: $DEEP_OUTPUT" >> "$LOG_FILE"
    fi
else
    echo "[$TIMESTAMP] خطأ: ملف cleanup.php غير موجود" >> "$LOG_FILE"
fi

# تنظيف السجلات القديمة (أكثر من 30 يوم)
find "$SYSTEM_PATH/logs" -name "*.log" -type f -mtime +30 -delete 2>/dev/null

# كتابة نهاية العملية في السجل
END_TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "[$END_TIMESTAMP] انتهاء عملية التنظيف التلقائي" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# إرسال تقرير بالبريد الإلكتروني (اختياري)
# يمكن تفعيل هذا القسم إذا كنت تريد تلقي تقارير بالبريد
# EMAIL="<EMAIL>"
# if [ ! -z "$EMAIL" ]; then
#     SUBJECT="تقرير تنظيف نظام MikroSys - $(date '+%Y-%m-%d')"
#     tail -20 "$LOG_FILE" | mail -s "$SUBJECT" "$EMAIL"
# fi

exit 0
