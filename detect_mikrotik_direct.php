<?php
/**
 * اكتشاف الأجهزة المتصلة بسيرفر MikroTik مباشرة عبر API
 */

// تعطيل عرض الأخطاء لضمان JSON صحيح
error_reporting(0);
ini_set('display_errors', 0);

// تعيين نوع المحتوى
header('Content-Type: application/json');

try {
    require_once 'config.php';
    require_once 'functions.php';
    
    // دالة للاتصال بـ MikroTik واكتشاف الأجهزة المتصلة
    function detectConnectedDevices() {
        $devices = [];
        
        try {
            if (!file_exists('mikrotik_api.php')) {
                throw new Exception('ملف MikroTik API غير موجود');
            }
            
            require_once 'mikrotik_api.php';
            $api = new RouterosAPI();
            $api->debug = false;
            
            // الحصول على معلومات الاتصال
            $connection_info = getMikroTikConnectionInfo();
            
            if (!$api->connect($connection_info['host'], MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
                throw new Exception('فشل الاتصال بسيرفر MikroTik');
            }
            
            // 1. الحصول على معلومات الواجهات (Interfaces)
            $interfaces = $api->comm('/interface/print');
            $router_interfaces = [];
            
            if (!empty($interfaces)) {
                foreach ($interfaces as $interface) {
                    if (isset($interface['mac-address']) && !empty($interface['mac-address'])) {
                        $router_interfaces[] = [
                            'name' => $interface['name'] ?? 'Unknown',
                            'mac' => strtoupper($interface['mac-address']),
                            'type' => $interface['type'] ?? 'Unknown',
                            'running' => isset($interface['running']) ? $interface['running'] : 'false'
                        ];
                    }
                }
            }
            
            // 2. الحصول على جدول ARP من MikroTik
            $arp_entries = $api->comm('/ip/arp/print');
            $connected_devices = [];
            
            if (!empty($arp_entries)) {
                foreach ($arp_entries as $entry) {
                    if (isset($entry['mac-address']) && isset($entry['address'])) {
                        $connected_devices[] = [
                            'ip' => $entry['address'],
                            'mac' => strtoupper($entry['mac-address']),
                            'interface' => $entry['interface'] ?? 'Unknown',
                            'status' => $entry['status'] ?? 'Unknown',
                            'dynamic' => isset($entry['dynamic']) ? 'Yes' : 'No'
                        ];
                    }
                }
            }
            
            // 3. الحصول على DHCP Leases (الأجهزة التي حصلت على IP من DHCP)
            $dhcp_leases = [];
            try {
                $leases = $api->comm('/ip/dhcp-server/lease/print');
                if (!empty($leases)) {
                    foreach ($leases as $lease) {
                        if (isset($lease['mac-address']) && isset($lease['address'])) {
                            $dhcp_leases[] = [
                                'ip' => $lease['address'],
                                'mac' => strtoupper($lease['mac-address']),
                                'hostname' => $lease['host-name'] ?? '',
                                'server' => $lease['server'] ?? 'Unknown',
                                'status' => $lease['status'] ?? 'Unknown'
                            ];
                        }
                    }
                }
            } catch (Exception $e) {
                // DHCP قد لا يكون مفعل
            }
            
            // 4. الحصول على Wireless Registration Table (الأجهزة المتصلة بالواي فاي)
            $wireless_devices = [];
            try {
                $wireless_regs = $api->comm('/interface/wireless/registration-table/print');
                if (!empty($wireless_regs)) {
                    foreach ($wireless_regs as $reg) {
                        if (isset($reg['mac-address'])) {
                            $wireless_devices[] = [
                                'mac' => strtoupper($reg['mac-address']),
                                'interface' => $reg['interface'] ?? 'Unknown',
                                'signal_strength' => $reg['signal-strength'] ?? 'Unknown',
                                'tx_rate' => $reg['tx-rate'] ?? 'Unknown',
                                'rx_rate' => $reg['rx-rate'] ?? 'Unknown',
                                'uptime' => $reg['uptime'] ?? 'Unknown'
                            ];
                        }
                    }
                }
            } catch (Exception $e) {
                // Wireless قد لا يكون مفعل
            }
            
            // 5. الحصول على معلومات النظام
            $system_info = [];
            try {
                $identity = $api->comm('/system/identity/print');
                $resource = $api->comm('/system/resource/print');
                
                if (!empty($identity)) {
                    $system_info['identity'] = $identity[0]['name'] ?? 'MikroTik';
                }
                
                if (!empty($resource)) {
                    $system_info['version'] = $resource[0]['version'] ?? 'Unknown';
                    $system_info['board'] = $resource[0]['board-name'] ?? 'Unknown';
                    $system_info['uptime'] = $resource[0]['uptime'] ?? 'Unknown';
                }
            } catch (Exception $e) {
                // معلومات النظام اختيارية
            }
            
            $api->disconnect();
            
            // دمج جميع المعلومات
            $all_devices = [];
            
            // إضافة الراوتر نفسه
            if (!empty($router_interfaces)) {
                foreach ($router_interfaces as $interface) {
                    $all_devices[] = [
                        'ip' => $connection_info['host'],
                        'mac' => $interface['mac'],
                        'hostname' => $system_info['identity'] ?? 'MikroTik Router',
                        'type' => 'Router Interface',
                        'interface' => $interface['name'],
                        'status' => $interface['running'] === 'true' ? 'Active' : 'Inactive',
                        'method' => 'router_interface'
                    ];
                }
            }
            
            // إضافة الأجهزة المتصلة من ARP
            foreach ($connected_devices as $device) {
                $all_devices[] = [
                    'ip' => $device['ip'],
                    'mac' => $device['mac'],
                    'hostname' => '',
                    'type' => 'Connected Device',
                    'interface' => $device['interface'],
                    'status' => $device['status'],
                    'method' => 'arp_table'
                ];
            }
            
            // إضافة أجهزة DHCP
            foreach ($dhcp_leases as $device) {
                $all_devices[] = [
                    'ip' => $device['ip'],
                    'mac' => $device['mac'],
                    'hostname' => $device['hostname'],
                    'type' => 'DHCP Client',
                    'interface' => 'DHCP',
                    'status' => $device['status'],
                    'method' => 'dhcp_lease'
                ];
            }
            
            // إضافة الأجهزة اللاسلكية
            foreach ($wireless_devices as $device) {
                $all_devices[] = [
                    'ip' => '',
                    'mac' => $device['mac'],
                    'hostname' => '',
                    'type' => 'Wireless Client',
                    'interface' => $device['interface'],
                    'status' => 'Connected',
                    'signal_strength' => $device['signal_strength'],
                    'method' => 'wireless_registration'
                ];
            }
            
            // إزالة التكرارات بناءً على MAC Address
            $unique_devices = [];
            foreach ($all_devices as $device) {
                $mac = $device['mac'];
                if (!isset($unique_devices[$mac])) {
                    $unique_devices[$mac] = $device;
                } else {
                    // دمج المعلومات (إعطاء أولوية للمعلومات الأكثر تفصيلاً)
                    if (empty($unique_devices[$mac]['hostname']) && !empty($device['hostname'])) {
                        $unique_devices[$mac]['hostname'] = $device['hostname'];
                    }
                    if (empty($unique_devices[$mac]['ip']) && !empty($device['ip'])) {
                        $unique_devices[$mac]['ip'] = $device['ip'];
                    }
                }
            }
            
            return [
                'devices' => array_values($unique_devices),
                'system_info' => $system_info,
                'stats' => [
                    'total_devices' => count($unique_devices),
                    'router_interfaces' => count($router_interfaces),
                    'arp_entries' => count($connected_devices),
                    'dhcp_leases' => count($dhcp_leases),
                    'wireless_clients' => count($wireless_devices)
                ]
            ];
            
        } catch (Exception $e) {
            throw new Exception('خطأ في اكتشاف الأجهزة: ' . $e->getMessage());
        }
    }
    
    // تشغيل الاكتشاف
    $result = detectConnectedDevices();
    
    // ترتيب الأجهزة حسب IP
    usort($result['devices'], function($a, $b) {
        if (empty($a['ip']) && empty($b['ip'])) return 0;
        if (empty($a['ip'])) return 1;
        if (empty($b['ip'])) return -1;
        return ip2long($a['ip']) - ip2long($b['ip']);
    });
    
    echo json_encode([
        'success' => true,
        'devices' => $result['devices'],
        'system_info' => $result['system_info'],
        'stats' => $result['stats'],
        'count' => count($result['devices']),
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => 'تم اكتشاف ' . count($result['devices']) . ' جهاز متصل بسيرفر MikroTik'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'devices' => [],
        'count' => 0,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في النظام: ' . $e->getMessage(),
        'devices' => [],
        'count' => 0,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
