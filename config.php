<?php
// تعطيل عرض الأخطاء في الإنتاج
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'mikrosys');
define('DB_PASS', 'mikrosys@2025');
define('DB_NAME', 'mikrosys');

// إعدادات MikroTik
define('MIKROTIK_HOST', '**********');
define('MIKROTIK_MAC', ''); // MAC Address للاتصال (اختياري)
define('MIKROTIK_CONNECTION_TYPE', 'ip'); // ip أو mac
define('MIKROTIK_USER', 'mohager');
define('MIKROTIK_PASS', 'P@$$w0rd');
define('MIKROTIK_PORT', 8729);

// إعدادات النظام
define('SY<PERSON>EM_NAME', 'MikroSys Hotspot Manager');
define('CARDS_PER_PAGE', 50);
define('DEFAULT_PROFILE', '24hours');

// دالة لتسجيل الأخطاء
function logError($message) {
    $log_file = 'logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message" . PHP_EOL;

    // إنشاء مجلد اللوجات إذا لم يكن موجوداً
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }

    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// متغير الاتصال العام
$conn = null;

try {
    // محاولة الاتصال بقاعدة البيانات
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

    // التحقق من وجود خطأ في الاتصال
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // تعيين الترميز
    $conn->set_charset("utf8");

} catch (Exception $e) {
    // في حالة فشل الاتصال، محاولة إنشاء قاعدة البيانات
    logError("Database connection failed: " . $e->getMessage());

    try {
        // الاتصال بدون تحديد قاعدة البيانات
        $temp_conn = new mysqli(DB_HOST, DB_USER, DB_PASS);

        if ($temp_conn->connect_error) {
            throw new Exception("Cannot connect to MySQL server: " . $temp_conn->connect_error);
        }

        $temp_conn->set_charset("utf8");

        // إنشاء قاعدة البيانات
        $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8 COLLATE utf8_general_ci";
        if (!$temp_conn->query($sql)) {
            throw new Exception("Error creating database: " . $temp_conn->error);
        }

        $temp_conn->close();

        // إعادة محاولة الاتصال
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($conn->connect_error) {
            throw new Exception("Connection failed after creating database: " . $conn->connect_error);
        }

        $conn->set_charset("utf8");

    } catch (Exception $e2) {
        logError("Critical database error: " . $e2->getMessage());
        $conn = null;
    }
}

// إنشاء الجداول إذا كان الاتصال ناجحاً
if ($conn) {
    try {
        // إنشاء جدول الكروت
        $sql_cards = "CREATE TABLE IF NOT EXISTS hotspot_cards (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(50) NOT NULL,
            profile VARCHAR(50) NOT NULL,
            status ENUM('active', 'used', 'expired') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            used_at TIMESTAMP NULL,
            mikrotik_id VARCHAR(50) NULL,
            notes TEXT NULL,
            INDEX idx_status (status),
            INDEX idx_created_at (created_at),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

        if (!$conn->query($sql_cards)) {
            logError("Error creating hotspot_cards table: " . $conn->error);
        }

        // إنشاء جدول البروفايلات
        $sql_profiles = "CREATE TABLE IF NOT EXISTS hotspot_profiles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            display_name VARCHAR(100) NOT NULL,
            session_timeout INT DEFAULT 0,
            idle_timeout INT DEFAULT 0,
            rate_limit VARCHAR(50) DEFAULT '',
            shared_users INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

        if (!$conn->query($sql_profiles)) {
            logError("Error creating hotspot_profiles table: " . $conn->error);
        }

        // إنشاء جدول الإعدادات
        $sql_settings = "CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

        if (!$conn->query($sql_settings)) {
            logError("Error creating system_settings table: " . $conn->error);
        }

        // إدراج البروفايلات الافتراضية
        $default_profiles = [
            ['1hour', 'ساعة واحدة', 3600, 300, '1M/1M', 1],
            ['3hours', '3 ساعات', 10800, 300, '2M/2M', 1],
            ['6hours', '6 ساعات', 21600, 600, '3M/3M', 1],
            ['12hours', '12 ساعة', 43200, 600, '5M/5M', 1],
            ['24hours', '24 ساعة', 86400, 900, '10M/10M', 1],
            ['7days', 'أسبوع', 604800, 1800, '15M/15M', 2],
            ['30days', 'شهر', 2592000, 3600, '20M/20M', 3]
        ];

        $stmt = $conn->prepare("INSERT IGNORE INTO hotspot_profiles (name, display_name, session_timeout, idle_timeout, rate_limit, shared_users) VALUES (?, ?, ?, ?, ?, ?)");

        if ($stmt) {
            foreach ($default_profiles as $profile) {
                $stmt->bind_param('ssiisi', $profile[0], $profile[1], $profile[2], $profile[3], $profile[4], $profile[5]);
                $stmt->execute();
            }
            $stmt->close();
        } else {
            logError("Error preparing statement for profiles: " . $conn->error);
        }

    } catch (Exception $e) {
        logError("Error setting up database tables: " . $e->getMessage());
    }
}

// دالة للحصول على إعداد من قاعدة البيانات
function getSetting($key, $default = '') {
    global $conn;

    if (!$conn) {
        return $default;
    }

    try {
        $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        if ($stmt) {
            $stmt->bind_param('s', $key);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                $stmt->close();
                return $row['setting_value'];
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        logError("Error getting setting '$key': " . $e->getMessage());
    }

    return $default;
}

// دالة لحفظ إعداد في قاعدة البيانات
function setSetting($key, $value) {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        if ($stmt) {
            $stmt->bind_param('sss', $key, $value, $value);
            $result = $stmt->execute();
            $stmt->close();
            return $result;
        }
    } catch (Exception $e) {
        logError("Error setting '$key': " . $e->getMessage());
    }

    return false;
}

// ملاحظة: دالة checkMikroTikConnection موجودة في functions.php
?>
