<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// معالجة الإعداد السريع
if ($_POST['action'] == 'quick_setup') {
    $host = validateInput($_POST['host']);
    $user = validateInput($_POST['user']);
    $pass = $_POST['pass'];
    $port = validateInput($_POST['port'], 'int');
    
    // حفظ الإعدادات
    setSetting('mikrotik_host', $host);
    setSetting('mikrotik_user', $user);
    setSetting('mikrotik_pass', $pass);
    setSetting('mikrotik_port', $port);
    setSetting('connection_type', 'ip');
    
    // اختبار الاتصال فوراً
    try {
        require_once 'mikrotik_api.php';
        $api = new RouterosAPI();
        $api->debug = false;
        
        if ($api->connect($host, $user, $pass, $port)) {
            // الحصول على معلومات النظام
            $identity = $api->comm('/system/identity/print');
            $resource = $api->comm('/system/resource/print');
            
            $router_name = !empty($identity) ? $identity[0]['name'] : 'MikroTik';
            $router_version = !empty($resource) ? $resource[0]['version'] : 'غير معروف';
            
            $api->disconnect();
            
            $_SESSION['success'] = "تم الاتصال بنجاح! الراوتر: $router_name (الإصدار: $router_version)";
        } else {
            $_SESSION['error'] = "تم حفظ الإعدادات لكن فشل الاتصال. تحقق من البيانات.";
        }
    } catch (Exception $e) {
        $_SESSION['error'] = "تم حفظ الإعدادات لكن حدث خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعداد السريع - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .setup-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 30px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-setup {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            width: 100%;
        }
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h2><i class="fas fa-rocket"></i> الإعداد السريع</h2>
                <p class="mb-0">اتصل بسيرفر MikroTik في خطوات بسيطة</p>
            </div>
            
            <div class="setup-body">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <hr>
                        <div class="text-center">
                            <a href="index.php" class="btn btn-success me-2">
                                <i class="fas fa-home"></i> الصفحة الرئيسية
                            </a>
                            <a href="settings.php" class="btn btn-outline-success">
                                <i class="fas fa-cog"></i> الإعدادات المتقدمة
                            </a>
                        </div>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <form method="POST">
                    <input type="hidden" name="action" value="quick_setup">
                    
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-server text-primary"></i> عنوان IP لجهاز MikroTik
                        </label>
                        <input type="text" name="host" class="form-control" 
                               value="***********" placeholder="***********" required>
                        <div class="form-text">أدخل عنوان IP الخاص بجهاز MikroTik</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-user text-success"></i> اسم المستخدم
                        </label>
                        <input type="text" name="user" class="form-control" 
                               value="admin" placeholder="admin" required>
                        <div class="form-text">اسم المستخدم في MikroTik (عادة admin)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-key text-warning"></i> كلمة المرور
                        </label>
                        <input type="password" name="pass" class="form-control" 
                               placeholder="أدخل كلمة المرور" required>
                        <div class="form-text">كلمة مرور المستخدم في MikroTik</div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-plug text-info"></i> منفذ API
                        </label>
                        <input type="number" name="port" class="form-control" 
                               value="8728" placeholder="8728" required>
                        <div class="form-text">منفذ API (افتراضي: 8728)</div>
                    </div>
                    
                    <button type="submit" class="btn btn-setup">
                        <i class="fas fa-rocket"></i> اتصل الآن
                    </button>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <h6 class="text-muted">هل تحتاج مساعدة؟</h6>
                    <div class="row">
                        <div class="col-6">
                            <a href="debug_connection.php" class="btn btn-outline-warning btn-sm w-100">
                                <i class="fas fa-bug"></i> تشخيص
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="simple_settings.php" class="btn btn-outline-info btn-sm w-100">
                                <i class="fas fa-cog"></i> إعدادات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6><i class="fas fa-info-circle"></i> نصائح للإعداد</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>في MikroTik:</h6>
                                <div class="bg-light p-2 rounded">
                                    <code>/ip service enable api</code><br>
                                    <code>/user set admin password=123456</code>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>مشاكل شائعة:</h6>
                                <ul class="list-unstyled">
                                    <li>• تأكد من تفعيل API</li>
                                    <li>• تحقق من عنوان IP</li>
                                    <li>• تأكد من كلمة المرور</li>
                                    <li>• فحص Firewall</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
