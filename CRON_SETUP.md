# إعداد Cron Jobs لنظام MikroSys

هذا الدليل يوضح كيفية إعداد المهام المجدولة (Cron Jobs) لتشغيل عمليات التنظيف التلقائي.

## 1. إعداد سكريبت التنظيف

### تحديث مسار النظام
افتح ملف `cron_cleanup.sh` وعدّل المسار:

```bash
# مسار مجلد النظام
SYSTEM_PATH="/path/to/mikrosys"
```

غيّر `/path/to/mikrosys` إلى المسار الفعلي لمجلد النظام.

### إعطاء صلاحيات التنفيذ
```bash
chmod +x cron_cleanup.sh
```

## 2. إعداد Cron Jobs

### فتح محرر Cron
```bash
crontab -e
```

### إضافة المهام المجدولة

#### تنظيف يومي (كل يوم في الساعة 2:00 صباحاً)
```bash
0 2 * * * /path/to/mikrosys/cron_cleanup.sh
```

#### تنظيف كل 6 ساعات
```bash
0 */6 * * * /path/to/mikrosys/cron_cleanup.sh
```

#### تنظيف كل ساعة
```bash
0 * * * * /path/to/mikrosys/cron_cleanup.sh
```

## 3. بدائل أخرى

### استخدام PHP مباشرة
```bash
# تنظيف يومي
0 2 * * * /usr/bin/php /path/to/mikrosys/cleanup.php

# تنظيف شامل أسبوعي (يوم الاثنين)
0 3 * * 1 /usr/bin/php /path/to/mikrosys/cleanup.php?cleanup_old=1
```

### استخدام wget أو curl
```bash
# تنظيف عبر HTTP
0 2 * * * wget -q -O /dev/null "http://yourdomain.com/mikrosys/cleanup.php?ajax=1"

# أو باستخدام curl
0 2 * * * curl -s "http://yourdomain.com/mikrosys/cleanup.php?ajax=1" > /dev/null
```

## 4. مراقبة السجلات

### عرض سجل التنظيف
```bash
tail -f /path/to/mikrosys/logs/cron_cleanup.log
```

### عرض آخر 50 سطر
```bash
tail -50 /path/to/mikrosys/logs/cron_cleanup.log
```

### البحث في السجلات
```bash
grep "خطأ" /path/to/mikrosys/logs/cron_cleanup.log
grep "$(date '+%Y-%m-%d')" /path/to/mikrosys/logs/cron_cleanup.log
```

## 5. إعدادات متقدمة

### إرسال تقارير بالبريد الإلكتروني
عدّل ملف `cron_cleanup.sh` وألغِ التعليق عن الأسطر التالية:

```bash
EMAIL="<EMAIL>"
if [ ! -z "$EMAIL" ]; then
    SUBJECT="تقرير تنظيف نظام MikroSys - $(date '+%Y-%m-%d')"
    tail -20 "$LOG_FILE" | mail -s "$SUBJECT" "$EMAIL"
fi
```

### تنظيف السجلات القديمة
السكريبت يحذف تلقائياً السجلات الأقدم من 30 يوم. يمكن تعديل هذا الرقم:

```bash
# حذف السجلات الأقدم من 7 أيام
find "$SYSTEM_PATH/logs" -name "*.log" -type f -mtime +7 -delete
```

## 6. استكشاف الأخطاء

### التحقق من تشغيل Cron
```bash
# عرض المهام المجدولة الحالية
crontab -l

# عرض سجل Cron
tail -f /var/log/cron
# أو
tail -f /var/log/syslog | grep CRON
```

### اختبار السكريبت يدوياً
```bash
# تشغيل السكريبت مباشرة
./cron_cleanup.sh

# تشغيل PHP مباشرة
php cleanup.php
```

### مشاكل شائعة وحلولها

#### 1. صلاحيات الملفات
```bash
# إعطاء صلاحيات للمجلد
chmod 755 /path/to/mikrosys
chmod 644 /path/to/mikrosys/*.php
chmod +x /path/to/mikrosys/cron_cleanup.sh
```

#### 2. مسار PHP غير صحيح
```bash
# العثور على مسار PHP
which php
# أو
whereis php
```

#### 3. متغيرات البيئة
أضف متغيرات البيئة في بداية ملف crontab:
```bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
SHELL=/bin/bash
```

## 7. مثال كامل لـ Crontab

```bash
# متغيرات البيئة
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
SHELL=/bin/bash
MAILTO=<EMAIL>

# تنظيف يومي في الساعة 2:00 صباحاً
0 2 * * * /path/to/mikrosys/cron_cleanup.sh

# نسخ احتياطي أسبوعي (يوم الأحد في الساعة 1:00 صباحاً)
0 1 * * 0 mysqldump -u username -p'password' mikrosys_hotspot > /backups/mikrosys_$(date +\%Y\%m\%d).sql

# تحديث الإحصائيات كل 15 دقيقة
*/15 * * * * /usr/bin/php /path/to/mikrosys/update_stats.php > /dev/null 2>&1
```

## 8. نصائح إضافية

### تشغيل مهام متعددة
```bash
# تنظيف + نسخ احتياطي + تحديث إحصائيات
0 2 * * * /path/to/mikrosys/cron_cleanup.sh && /path/to/mikrosys/backup.sh && /usr/bin/php /path/to/mikrosys/update_stats.php
```

### تأخير عشوائي لتجنب الحمل الزائد
```bash
# تأخير عشوائي حتى 30 دقيقة
0 2 * * * sleep $((RANDOM \% 1800)) && /path/to/mikrosys/cron_cleanup.sh
```

### تشغيل فقط في أيام العمل
```bash
# من الاثنين إلى الجمعة
0 2 * * 1-5 /path/to/mikrosys/cron_cleanup.sh
```

### تشغيل في أوقات مختلفة حسب اليوم
```bash
# تنظيف خفيف يومياً
0 2 * * * /usr/bin/php /path/to/mikrosys/cleanup.php

# تنظيف شامل أسبوعياً
0 3 * * 1 /usr/bin/php /path/to/mikrosys/cleanup.php?cleanup_old=1

# نسخ احتياطي شهري
0 1 1 * * /path/to/mikrosys/backup.sh
```

---

**ملاحظة:** تأكد من تعديل جميع المسارات وأسماء المستخدمين وكلمات المرور حسب إعدادات خادمك.
