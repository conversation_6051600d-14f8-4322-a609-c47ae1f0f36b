<?php
/**
 * تشخيص مفصل لمشكلة الاتصال
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 تشخيص مفصل للاتصال</h1>";

// تضمين الملفات
require_once 'config.php';

echo "<h2>1. فحص الإعدادات:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الإعداد</th><th>القيمة</th></tr>";
echo "<tr><td>MIKROTIK_HOST</td><td>" . MIKROTIK_HOST . "</td></tr>";
echo "<tr><td>MIKROTIK_USER</td><td>" . MIKROTIK_USER . "</td></tr>";
echo "<tr><td>MIKROTIK_PASS</td><td>" . (empty(MIKROTIK_PASS) ? 'فارغ' : '***') . "</td></tr>";
echo "<tr><td>MIKROTIK_PORT</td><td>" . MIKROTIK_PORT . "</td></tr>";
echo "</table>";

echo "<h2>2. اختبار الشبكة:</h2>";

// اختبار ping
echo "<h3>أ) اختبار Ping:</h3>";
$ping_result = shell_exec("ping -n 1 " . MIKROTIK_HOST . " 2>&1");
if ($ping_result) {
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($ping_result);
    echo "</pre>";
    
    if (strpos($ping_result, 'TTL=') !== false || strpos($ping_result, 'time=') !== false) {
        echo "<div style='color: green;'>✅ الراوتر متاح على الشبكة</div>";
    } else {
        echo "<div style='color: red;'>❌ الراوتر غير متاح على الشبكة</div>";
    }
} else {
    echo "<div style='color: orange;'>⚠️ لا يمكن تنفيذ أمر ping</div>";
}

// اختبار البورت
echo "<h3>ب) اختبار البورت:</h3>";
$socket = @fsockopen(MIKROTIK_HOST, MIKROTIK_PORT, $errno, $errstr, 5);
if ($socket) {
    echo "<div style='color: green;'>✅ البورت " . MIKROTIK_PORT . " مفتوح ومتاح</div>";
    fclose($socket);
} else {
    echo "<div style='color: red;'>❌ البورت " . MIKROTIK_PORT . " مغلق أو غير متاح</div>";
    echo "<div><strong>خطأ:</strong> $errstr ($errno)</div>";
}

echo "<h2>3. اختبار API مع تفاصيل:</h2>";

if (file_exists('mikrotik_api.php')) {
    require_once 'mikrotik_api.php';
    
    try {
        $api = new RouterosAPI();
        $api->debug = true; // تفعيل وضع التشخيص
        
        echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
        echo "<strong>محاولة الاتصال بالتفاصيل:</strong><br>";
        echo "Host: " . MIKROTIK_HOST . "<br>";
        echo "Port: " . MIKROTIK_PORT . "<br>";
        echo "User: " . MIKROTIK_USER . "<br>";
        echo "Pass: " . (empty(MIKROTIK_PASS) ? 'فارغ' : 'محدد') . "<br>";
        echo "</div>";
        
        // بدء التقاط المخرجات
        ob_start();
        
        $connection_result = $api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT);
        
        // الحصول على مخرجات التشخيص
        $debug_output = ob_get_clean();
        
        if (!empty($debug_output)) {
            echo "<h4>تفاصيل الاتصال:</h4>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; font-size: 12px;'>";
            echo htmlspecialchars($debug_output);
            echo "</pre>";
        }
        
        if ($connection_result) {
            echo "<div style='color: green; font-size: 18px; font-weight: bold;'>✅ تم الاتصال بنجاح!</div>";
            
            // اختبار أوامر بسيطة
            try {
                $identity = $api->comm('/system/identity/print');
                if (!empty($identity)) {
                    echo "<div><strong>اسم الراوتر:</strong> " . $identity[0]['name'] . "</div>";
                }
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ فشل في تنفيذ الأوامر: " . $e->getMessage() . "</div>";
            }
            
            $api->disconnect();
        } else {
            echo "<div style='color: red; font-size: 16px; font-weight: bold;'>❌ فشل الاتصال</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
        echo "<div><strong>تفاصيل الخطأ:</strong></div>";
        echo "<pre style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo htmlspecialchars($e->getTraceAsString());
        echo "</pre>";
    }
} else {
    echo "<div style='color: red;'>❌ ملف mikrotik_api.php غير موجود</div>";
}

echo "<h2>4. اختبار بديل - Telnet:</h2>";
echo "<div>جرب الاتصال عبر Telnet للتأكد من أن الراوتر يقبل الاتصالات:</div>";
echo "<code>telnet " . MIKROTIK_HOST . " " . MIKROTIK_PORT . "</code>";

echo "<h2>5. فحص إعدادات MikroTik المطلوبة:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<strong>تأكد من الإعدادات التالية في MikroTik:</strong>";
echo "<ol>";
echo "<li><code>/ip service print</code> - للتأكد من تفعيل API</li>";
echo "<li><code>/ip service enable api</code> - لتفعيل API</li>";
echo "<li><code>/user print</code> - للتأكد من وجود المستخدم</li>";
echo "<li><code>/user add name=mohager password=P@\$\$w0rd group=full</code> - لإنشاء المستخدم</li>";
echo "</ol>";
echo "</div>";

echo "<h2>6. اختبار اتصال مبسط:</h2>";

// اختبار اتصال مبسط بدون كلاسات
$simple_socket = @fsockopen(MIKROTIK_HOST, MIKROTIK_PORT, $errno, $errstr, 10);
if ($simple_socket) {
    echo "<div style='color: green;'>✅ تم فتح Socket بنجاح</div>";
    
    // محاولة قراءة البيانات الأولية
    $initial_data = fread($simple_socket, 1024);
    if ($initial_data) {
        echo "<div><strong>البيانات المستلمة:</strong></div>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
        echo htmlspecialchars($initial_data);
        echo "</pre>";
    }
    
    fclose($simple_socket);
} else {
    echo "<div style='color: red;'>❌ فشل في فتح Socket: $errstr ($errno)</div>";
}

echo "<hr>";
echo "<h2>الخطوات المقترحة:</h2>";
echo "<ol>";
echo "<li>تحقق من أن الراوتر يعمل ومتصل بالشبكة</li>";
echo "<li>تأكد من تفعيل خدمة API في MikroTik</li>";
echo "<li>تحقق من صحة بيانات المستخدم</li>";
echo "<li>تأكد من عدم وجود جدار حماية يحجب البورت 8728</li>";
echo "<li>جرب الاتصال من جهاز آخر للتأكد</li>";
echo "</ol>";
?>
