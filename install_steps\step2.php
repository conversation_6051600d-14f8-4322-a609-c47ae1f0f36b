<h3><i class="fas fa-database"></i> إعداد قاعدة البيانات</h3>
<p>يرجى إدخال بيانات الاتصال بقاعدة البيانات MySQL.</p>

<form method="POST">
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">خادم قاعدة البيانات</label>
                <input type="text" name="db_host" class="form-control" value="localhost" required>
                <div class="form-text">عادة ما يكون localhost</div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">اسم قاعدة البيانات</label>
                <input type="text" name="db_name" class="form-control" value="mikrosys_hotspot" required>
                <div class="form-text">سيتم إنشاؤها إذا لم تكن موجودة</div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">اسم المستخدم</label>
                <input type="text" name="db_user" class="form-control" value="root" required>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">كلمة المرور</label>
                <input type="password" name="db_pass" class="form-control" placeholder="اتركها فارغة إذا لم تكن محددة">
            </div>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>ملاحظة:</strong> تأكد من أن المستخدم لديه صلاحيات إنشاء قواعد البيانات والجداول.
    </div>
    
    <div class="text-center">
        <a href="install.php?step=1" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> السابق
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-database"></i> إنشاء قاعدة البيانات
        </button>
    </div>
</form>

<div class="mt-4">
    <h5>ما سيتم إنشاؤه:</h5>
    <ul class="list-group list-group-flush">
        <li class="list-group-item">
            <i class="fas fa-table text-primary"></i>
            جدول <code>hotspot_cards</code> - لحفظ بيانات الكروت
        </li>
        <li class="list-group-item">
            <i class="fas fa-table text-primary"></i>
            جدول <code>hotspot_profiles</code> - لحفظ البروفايلات
        </li>
        <li class="list-group-item">
            <i class="fas fa-table text-primary"></i>
            جدول <code>system_settings</code> - لحفظ إعدادات النظام
        </li>
        <li class="list-group-item">
            <i class="fas fa-file-code text-success"></i>
            ملف <code>config.php</code> - ملف الإعدادات الرئيسي
        </li>
    </ul>
</div>
