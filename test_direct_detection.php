<?php
/**
 * اختبار الاكتشاف المباشر للأجهزة من سيرفر MikroTik
 */

session_start();
require_once 'config.php';
require_once 'functions.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاكتشاف المباشر - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔍 اختبار الاكتشاف المباشر من MikroTik</h1>
        
        <!-- معلومات الاتصال -->
        <div class="row justify-content-center mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-server"></i> معلومات الاتصال</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>إعدادات MikroTik:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Host:</strong> <?= MIKROTIK_HOST ?></li>
                                    <li><strong>Port:</strong> <?= MIKROTIK_PORT ?></li>
                                    <li><strong>User:</strong> <?= MIKROTIK_USER ?></li>
                                    <li><strong>نوع الاتصال:</strong> 
                                        <?= defined('MIKROTIK_CONNECTION_TYPE') ? MIKROTIK_CONNECTION_TYPE : 'ip' ?>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>حالة النظام:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>API File:</strong> 
                                        <?= file_exists('mikrotik_api.php') ? '✅ موجود' : '❌ غير موجود' ?>
                                    </li>
                                    <li><strong>قاعدة البيانات:</strong> 
                                        <?= $conn ? '✅ متصل' : '❌ غير متصل' ?>
                                    </li>
                                    <li><strong>Functions:</strong> 
                                        <?= function_exists('getMikroTikConnectionInfo') ? '✅ جاهز' : '❌ غير جاهز' ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button class="btn btn-primary" onclick="startDetection()">
                                <i class="fas fa-search"></i> بدء الاكتشاف
                            </button>
                            <button class="btn btn-secondary" onclick="testConnection()">
                                <i class="fas fa-plug"></i> اختبار الاتصال فقط
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نتائج الاكتشاف -->
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-list"></i> نتائج الاكتشاف</h5>
                    </div>
                    <div class="card-body">
                        <div id="detection_results">
                            <div class="text-center text-muted">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <p>اضغط "بدء الاكتشاف" لعرض الأجهزة المتصلة بسيرفر MikroTik</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6><i class="fas fa-info-circle"></i> ما يتم اكتشافه</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <h6><i class="fas fa-network-wired"></i> واجهات الراوتر:</h6>
                                <ul class="list-unstyled">
                                    <li>• Ethernet interfaces</li>
                                    <li>• Wireless interfaces</li>
                                    <li>• Bridge interfaces</li>
                                    <li>• VLAN interfaces</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-3">
                                <h6><i class="fas fa-table"></i> جدول ARP:</h6>
                                <ul class="list-unstyled">
                                    <li>• الأجهزة المتصلة</li>
                                    <li>• عناوين IP و MAC</li>
                                    <li>• حالة الاتصال</li>
                                    <li>• الواجهة المستخدمة</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-3">
                                <h6><i class="fas fa-dharmachakra"></i> عملاء DHCP:</h6>
                                <ul class="list-unstyled">
                                    <li>• الأجهزة التي حصلت على IP</li>
                                    <li>• أسماء الأجهزة</li>
                                    <li>• حالة الإيجار</li>
                                    <li>• خادم DHCP</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-3">
                                <h6><i class="fas fa-wifi"></i> العملاء اللاسلكيين:</h6>
                                <ul class="list-unstyled">
                                    <li>• الأجهزة المتصلة بالواي فاي</li>
                                    <li>• قوة الإشارة</li>
                                    <li>• سرعة الإرسال والاستقبال</li>
                                    <li>• مدة الاتصال</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-10">
                <div class="text-center">
                    <a href="settings.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                    <a href="test_mac_connection.php" class="btn btn-outline-info me-2">
                        <i class="fas fa-network-wired"></i> اختبار MAC
                    </a>
                    <a href="detect_mikrotik_direct.php" class="btn btn-outline-success me-2" target="_blank">
                        <i class="fas fa-code"></i> API مباشر
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">تم إنشاء هذا التقرير في: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startDetection() {
            const resultDiv = document.getElementById('detection_results');
            resultDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-3">جاري الاتصال بسيرفر MikroTik واكتشاف الأجهزة المتصلة...</p>
                    <small class="text-muted">قد يستغرق هذا بضع ثوان</small>
                </div>
            `;
            
            fetch('detect_mikrotik_direct.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayResults(data);
                    } else {
                        displayError(data.error || 'فشل في الاكتشاف');
                    }
                })
                .catch(error => {
                    displayError('خطأ في الاتصال: ' + error.message);
                });
        }
        
        function testConnection() {
            const resultDiv = document.getElementById('detection_results');
            resultDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-info" role="status"></div>
                    <p class="mt-3">جاري اختبار الاتصال بسيرفر MikroTik...</p>
                </div>
            `;
            
            // اختبار بسيط للاتصال
            fetch('detect_mikrotik_direct.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> تم الاتصال بنجاح!</h5>
                                <p><strong>اسم الراوتر:</strong> ${data.system_info?.identity || 'MikroTik'}</p>
                                <p><strong>الإصدار:</strong> ${data.system_info?.version || 'غير معروف'}</p>
                                <p><strong>عدد الأجهزة:</strong> ${data.count}</p>
                            </div>
                        `;
                    } else {
                        displayError(data.error || 'فشل الاتصال');
                    }
                })
                .catch(error => {
                    displayError('خطأ في الاتصال: ' + error.message);
                });
        }
        
        function displayResults(data) {
            let html = `
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> تم الاكتشاف بنجاح!</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الراوتر:</strong> ${data.system_info?.identity || 'MikroTik'}</p>
                            <p><strong>الإصدار:</strong> ${data.system_info?.version || 'غير معروف'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>إجمالي الأجهزة:</strong> ${data.count}</p>
                            <p><strong>وقت الاكتشاف:</strong> ${data.timestamp}</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-primary">${data.stats?.router_interfaces || 0}</h5>
                                <small>واجهات الراوتر</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-success">${data.stats?.dhcp_leases || 0}</h5>
                                <small>عملاء DHCP</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-info">${data.stats?.wireless_clients || 0}</h5>
                                <small>عملاء لاسلكيين</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-secondary">${data.stats?.arp_entries || 0}</h5>
                                <small>إدخالات ARP</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h6>قائمة الأجهزة المكتشفة:</h6>
                <div style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>MAC Address</th>
                                <th>IP Address</th>
                                <th>اسم الجهاز</th>
                                <th>النوع</th>
                                <th>الواجهة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            data.devices.forEach(device => {
                let typeClass = 'secondary';
                if (device.type === 'Router Interface') typeClass = 'primary';
                else if (device.type === 'DHCP Client') typeClass = 'success';
                else if (device.type === 'Wireless Client') typeClass = 'info';
                
                html += `
                    <tr>
                        <td><code>${device.mac}</code></td>
                        <td>${device.ip || '-'}</td>
                        <td>${device.hostname || '-'}</td>
                        <td><span class="badge bg-${typeClass}">${device.type}</span></td>
                        <td>${device.interface || '-'}</td>
                        <td>${device.status || '-'}</td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            document.getElementById('detection_results').innerHTML = html;
        }
        
        function displayError(error) {
            document.getElementById('detection_results').innerHTML = `
                <div class="alert alert-danger">
                    <h5><i class="fas fa-times-circle"></i> فشل في الاكتشاف</h5>
                    <p>${error}</p>
                    <hr>
                    <h6>تحقق من:</h6>
                    <ul>
                        <li>صحة إعدادات MikroTik في صفحة الإعدادات</li>
                        <li>تفعيل API في MikroTik</li>
                        <li>صحة اسم المستخدم وكلمة المرور</li>
                        <li>الاتصال بالشبكة</li>
                    </ul>
                </div>
            `;
        }
    </script>
</body>
</html>
