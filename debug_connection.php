<?php
/**
 * تشخيص مشكلة الاتصال بـ MikroTik
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';
require_once 'functions.php';

echo "<h1>🔧 تشخيص مشكلة الاتصال بـ MikroTik</h1>";

echo "<h3>1. الثوابت المعرفة:</h3>";
echo "<ul>";
echo "<li><strong>MIKROTIK_HOST:</strong> " . (defined('MIKROTIK_HOST') ? MIKROTIK_HOST : 'غير معرف') . "</li>";
echo "<li><strong>MIKROTIK_USER:</strong> " . (defined('MIKROTIK_USER') ? MIKROTIK_USER : 'غير معرف') . "</li>";
echo "<li><strong>MIKROTIK_PORT:</strong> " . (defined('MIKROTIK_PORT') ? MIKROTIK_PORT : 'غير معرف') . "</li>";
echo "<li><strong>MIKROTIK_CONNECTION_TYPE:</strong> " . (defined('MIKROTIK_CONNECTION_TYPE') ? MIKROTIK_CONNECTION_TYPE : 'غير معرف') . "</li>";
echo "<li><strong>MIKROTIK_MAC:</strong> " . (defined('MIKROTIK_MAC') ? MIKROTIK_MAC : 'غير معرف') . "</li>";
echo "</ul>";

echo "<h3>2. الإعدادات من قاعدة البيانات:</h3>";
echo "<ul>";
echo "<li><strong>mikrotik_host:</strong> " . getSetting('mikrotik_host', 'غير موجود') . "</li>";
echo "<li><strong>mikrotik_user:</strong> " . getSetting('mikrotik_user', 'غير موجود') . "</li>";
echo "<li><strong>mikrotik_port:</strong> " . getSetting('mikrotik_port', 'غير موجود') . "</li>";
echo "<li><strong>connection_type:</strong> " . getSetting('connection_type', 'غير موجود') . "</li>";
echo "<li><strong>mikrotik_mac:</strong> " . getSetting('mikrotik_mac', 'غير موجود') . "</li>";
echo "</ul>";

echo "<h3>3. معلومات الاتصال المحسوبة:</h3>";
try {
    $connection_info = getMikroTikConnectionInfo();
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . $connection_info['host'] . "</li>";
    echo "<li><strong>Connection Type:</strong> " . $connection_info['connection_type'] . "</li>";
    echo "<li><strong>Original Host:</strong> " . $connection_info['original_host'] . "</li>";
    echo "<li><strong>MAC Address:</strong> " . ($connection_info['mac_address'] ?: 'فارغ') . "</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<div style='color: red;'>خطأ في getMikroTikConnectionInfo: " . $e->getMessage() . "</div>";
}

echo "<h3>4. اختبار ملف MikroTik API:</h3>";
if (file_exists('mikrotik_api.php')) {
    echo "✅ ملف mikrotik_api.php موجود<br>";
    
    try {
        require_once 'mikrotik_api.php';
        if (class_exists('RouterosAPI')) {
            echo "✅ فئة RouterosAPI متاحة<br>";
        } else {
            echo "❌ فئة RouterosAPI غير متاحة<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في تحميل mikrotik_api.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف mikrotik_api.php غير موجود<br>";
}

echo "<h3>5. اختبار الاتصال المباشر:</h3>";
try {
    if (!file_exists('mikrotik_api.php')) {
        throw new Exception('ملف MikroTik API غير موجود');
    }
    
    require_once 'mikrotik_api.php';
    $api = new RouterosAPI();
    $api->debug = false;
    
    $connection_info = getMikroTikConnectionInfo();
    $host = $connection_info['host'];
    $user = getSetting('mikrotik_user', MIKROTIK_USER);
    $pass = getSetting('mikrotik_pass', MIKROTIK_PASS);
    $port = getSetting('mikrotik_port', MIKROTIK_PORT);
    
    echo "<p><strong>محاولة الاتصال بـ:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> $host</li>";
    echo "<li><strong>User:</strong> $user</li>";
    echo "<li><strong>Port:</strong> $port</li>";
    echo "<li><strong>Password:</strong> " . (empty($pass) ? 'فارغ' : '***') . "</li>";
    echo "</ul>";
    
    if ($api->connect($host, $user, $pass, $port)) {
        echo "<div style='color: green; font-weight: bold;'>✅ تم الاتصال بنجاح!</div>";
        
        // اختبار بعض الأوامر
        try {
            $identity = $api->comm('/system/identity/print');
            if (!empty($identity)) {
                echo "<p><strong>اسم الراوتر:</strong> " . $identity[0]['name'] . "</p>";
            }
            
            $resource = $api->comm('/system/resource/print');
            if (!empty($resource)) {
                echo "<p><strong>إصدار RouterOS:</strong> " . $resource[0]['version'] . "</p>";
                echo "<p><strong>نوع الجهاز:</strong> " . $resource[0]['board-name'] . "</p>";
            }
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ تم الاتصال لكن فشل في تنفيذ الأوامر: " . $e->getMessage() . "</div>";
        }
        
        $api->disconnect();
    } else {
        echo "<div style='color: red; font-weight: bold;'>❌ فشل الاتصال</div>";
        echo "<p>الأسباب المحتملة:</p>";
        echo "<ul>";
        echo "<li>عنوان IP خاطئ</li>";
        echo "<li>API غير مفعل في MikroTik</li>";
        echo "<li>اسم المستخدم أو كلمة المرور خاطئة</li>";
        echo "<li>منفذ API خاطئ (افتراضي: 8728)</li>";
        echo "<li>Firewall يحجب الاتصال</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</div>";
}

echo "<h3>6. اختبار دوال التحقق:</h3>";

// اختبار دالة isValidMacAddress
echo "<h4>اختبار دالة isValidMacAddress:</h4>";
$test_macs = [
    'AA:BB:CC:DD:EE:FF',
    'aa:bb:cc:dd:ee:ff',
    'AA-BB-CC-DD-EE-FF',
    'AABBCCDDEEFF',
    'invalid-mac',
    ''
];

foreach ($test_macs as $mac) {
    $result = function_exists('isValidMacAddress') ? isValidMacAddress($mac) : 'دالة غير موجودة';
    echo "<li><strong>$mac:</strong> " . ($result ? 'صحيح' : 'خاطئ') . "</li>";
}

echo "<h3>7. حلول مقترحة:</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #ccc; border-radius: 5px;'>";
echo "<h4>إذا كان الاتصال يفشل:</h4>";
echo "<ol>";
echo "<li><strong>تحقق من إعدادات MikroTik:</strong>";
echo "<pre>/ip service print
/ip service enable api
/ip service set api port=8728</pre>";
echo "</li>";
echo "<li><strong>تحقق من المستخدم:</strong>";
echo "<pre>/user print
/user set admin password=yourpassword</pre>";
echo "</li>";
echo "<li><strong>اختبر الاتصال من الكمبيوتر:</strong>";
echo "<pre>ping " . (isset($connection_info) ? $connection_info['host'] : 'MIKROTIK_IP') . "</pre>";
echo "</li>";
echo "<li><strong>تحقق من Firewall:</strong>";
echo "<pre>/ip firewall filter print</pre>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<h3>8. روابط مفيدة:</h3>";
echo "<a href='settings.php' style='margin: 5px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>الإعدادات</a>";
echo "<a href='quick_test.php' style='margin: 5px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>اختبار سريع</a>";
echo "<a href='index.php' style='margin: 5px; padding: 10px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>الصفحة الرئيسية</a>";

echo "<br><br><small style='color: #666;'>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small>";
?>
