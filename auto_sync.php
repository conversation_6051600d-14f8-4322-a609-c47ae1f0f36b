<?php
/**
 * ملف المزامنة التلقائية للبروفايلات
 * يتم استدعاؤه عند بدء تشغيل النظام أو بشكل دوري
 */

require_once 'config.php';
require_once 'functions.php';

// دالة للمزامنة التلقائية عند بدء التشغيل
function autoSyncOnStartup() {
    // التحقق من آخر مزامنة
    $last_sync = getSetting('last_auto_sync', '0');
    $current_time = time();
    
    // مزامنة كل 24 ساعة (86400 ثانية)
    $sync_interval = 86400;
    
    if (($current_time - intval($last_sync)) > $sync_interval) {
        // حان وقت المزامنة
        $result = performAutoSync();
        
        if ($result['success']) {
            // تحديث وقت آخر مزامنة
            setSetting('last_auto_sync', $current_time);
            logError("Auto sync completed: " . $result['message']);
        } else {
            logError("Auto sync failed: " . $result['message']);
        }
        
        return $result;
    }
    
    return ['success' => true, 'message' => 'No sync needed'];
}

// دالة تنفيذ المزامنة التلقائية
function performAutoSync() {
    try {
        // التحقق من الاتصال بـ MikroTik
        if (!checkMikroTikConnection()) {
            return [
                'success' => false,
                'message' => 'Cannot connect to MikroTik server'
            ];
        }
        
        // مزامنة البروفايلات المحلية إلى MikroTik
        $synced_count = syncAllProfilesToMikroTik();
        
        if ($synced_count === false) {
            return [
                'success' => false,
                'message' => 'Failed to sync profiles to MikroTik'
            ];
        }
        
        return [
            'success' => true,
            'message' => "Synced $synced_count profiles to MikroTik"
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Auto sync error: ' . $e->getMessage()
        ];
    }
}

// دالة للمزامنة الفورية (عند إنشاء/تحديث/حذف بروفايل)
function instantSync($action, $profile_name, $profile_data = null) {
    try {
        if (!checkMikroTikConnection()) {
            logError("Instant sync failed: Cannot connect to MikroTik for action '$action' on profile '$profile_name'");
            return false;
        }
        
        switch ($action) {
            case 'create':
            case 'update':
                if ($profile_data) {
                    $result = createMikroTikProfile(
                        $profile_name,
                        $profile_data['session_timeout'],
                        $profile_data['idle_timeout'],
                        $profile_data['rate_limit'],
                        $profile_data['shared_users']
                    );
                    
                    if ($result) {
                        logError("Instant sync: $action profile '$profile_name' successful");
                        return true;
                    } else {
                        logError("Instant sync: $action profile '$profile_name' failed");
                        return false;
                    }
                }
                break;
                
            case 'delete':
                $result = deleteMikroTikProfile($profile_name);
                if ($result) {
                    logError("Instant sync: delete profile '$profile_name' successful");
                    return true;
                } else {
                    logError("Instant sync: delete profile '$profile_name' failed");
                    return false;
                }
                break;
        }
        
        return false;
        
    } catch (Exception $e) {
        logError("Instant sync error for '$action' on '$profile_name': " . $e->getMessage());
        return false;
    }
}

// دالة للتحقق من حالة المزامنة
function getSyncStatus() {
    $local_profiles = getAvailableProfiles();
    $mikrotik_profiles = getMikroTikProfiles();
    
    $status = [
        'local_count' => count($local_profiles),
        'mikrotik_count' => count($mikrotik_profiles),
        'synced_count' => 0,
        'unsynced_local' => [],
        'unsynced_mikrotik' => [],
        'last_sync' => getSetting('last_auto_sync', '0'),
        'mikrotik_connected' => checkMikroTikConnection()
    ];
    
    // مقارنة البروفايلات
    foreach ($local_profiles as $local_profile) {
        $found_in_mikrotik = false;
        foreach ($mikrotik_profiles as $mikrotik_profile) {
            if ($local_profile['name'] == $mikrotik_profile['name']) {
                $found_in_mikrotik = true;
                $status['synced_count']++;
                break;
            }
        }
        
        if (!$found_in_mikrotik) {
            $status['unsynced_local'][] = $local_profile['name'];
        }
    }
    
    // البحث عن بروفايلات في MikroTik غير موجودة محلياً
    foreach ($mikrotik_profiles as $mikrotik_profile) {
        $found_locally = false;
        foreach ($local_profiles as $local_profile) {
            if ($mikrotik_profile['name'] == $local_profile['name']) {
                $found_locally = true;
                break;
            }
        }
        
        if (!$found_locally) {
            $status['unsynced_mikrotik'][] = $mikrotik_profile['name'];
        }
    }
    
    return $status;
}

// دالة لإنشاء تقرير المزامنة
function generateSyncReport() {
    $status = getSyncStatus();
    
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'summary' => [
            'total_local' => $status['local_count'],
            'total_mikrotik' => $status['mikrotik_count'],
            'synced' => $status['synced_count'],
            'sync_percentage' => $status['local_count'] > 0 ? round(($status['synced_count'] / $status['local_count']) * 100, 1) : 0
        ],
        'details' => $status,
        'recommendations' => []
    ];
    
    // إضافة توصيات
    if (!$status['mikrotik_connected']) {
        $report['recommendations'][] = 'تحقق من اتصال MikroTik';
    }
    
    if (!empty($status['unsynced_local'])) {
        $report['recommendations'][] = 'قم بمزامنة البروفايلات المحلية إلى MikroTik';
    }
    
    if (!empty($status['unsynced_mikrotik'])) {
        $report['recommendations'][] = 'فكر في استيراد البروفايلات من MikroTik';
    }
    
    if ($report['summary']['sync_percentage'] < 100) {
        $report['recommendations'][] = 'تشغيل مزامنة شاملة مطلوب';
    }
    
    return $report;
}

// تشغيل المزامنة التلقائية إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'auto_sync.php') {
    header('Content-Type: application/json');
    
    $action = $_GET['action'] ?? 'status';
    
    switch ($action) {
        case 'sync':
            $result = autoSyncOnStartup();
            echo json_encode($result);
            break;
            
        case 'force_sync':
            $result = performAutoSync();
            if ($result['success']) {
                setSetting('last_auto_sync', time());
            }
            echo json_encode($result);
            break;
            
        case 'status':
            $status = getSyncStatus();
            echo json_encode($status);
            break;
            
        case 'report':
            $report = generateSyncReport();
            echo json_encode($report);
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    exit;
}

// تشغيل المزامنة التلقائية عند تضمين الملف
if (!defined('SKIP_AUTO_SYNC')) {
    autoSyncOnStartup();
}
?>
