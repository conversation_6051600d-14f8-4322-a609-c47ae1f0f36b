<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// معالجة طلبات المزامنة
if ($_POST['action'] == 'sync_to_mikrotik') {
    $synced = syncAllProfilesToMikroTik();
    if ($synced !== false) {
        $_SESSION['success'] = "تم مزامنة $synced بروفايل مع MikroTik بنجاح";
    } else {
        $_SESSION['error'] = "فشل في مزامنة البروفايلات مع MikroTik";
    }
}

if ($_POST['action'] == 'sync_from_mikrotik') {
    $imported = importProfilesFromMikroTik();
    if ($imported !== false) {
        $_SESSION['success'] = "تم استيراد $imported بروفايل من MikroTik بنجاح";
    } else {
        $_SESSION['error'] = "فشل في استيراد البروفايلات من MikroTik";
    }
}

// الحصول على البروفايلات من قاعدة البيانات و MikroTik
$local_profiles = getAvailableProfiles();
$mikrotik_profiles = getMikroTikProfiles();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مزامنة البروفايلات - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="header-content">
                    <div class="logo-container">
                        <img src="assets/img/Logo.png" alt="MikroSys Logo" class="system-logo">
                    </div>
                    <div class="header-text">
                        <h1><i class="fas fa-sync-alt"></i> مزامنة البروفايلات</h1>
                        <p class="mb-0">مزامنة البروفايلات بين النظام وسيرفر MikroTik</p>
                    </div>
                    <div class="header-actions">
                        <a href="profiles.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="container p-4">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <!-- حالة الاتصال -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5><i class="fas fa-wifi"></i> حالة الاتصال</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>قاعدة البيانات المحلية:</h6>
                                        <?php if ($conn): ?>
                                            <span class="badge bg-success">متصل</span>
                                            <small class="text-muted">(<?= count($local_profiles) ?> بروفايل)</small>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير متصل</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6>سيرفر MikroTik:</h6>
                                        <?php if (checkMikroTikConnection()): ?>
                                            <span class="badge bg-success">متصل</span>
                                            <small class="text-muted">(<?= count($mikrotik_profiles) ?> بروفايل)</small>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير متصل</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار المزامنة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-upload"></i> رفع للـ MikroTik</h5>
                            </div>
                            <div class="card-body">
                                <p>رفع جميع البروفايلات من قاعدة البيانات المحلية إلى سيرفر MikroTik</p>
                                <form method="POST">
                                    <input type="hidden" name="action" value="sync_to_mikrotik">
                                    <button type="submit" class="btn btn-primary" <?= !checkMikroTikConnection() ? 'disabled' : '' ?>>
                                        <i class="fas fa-cloud-upload-alt"></i> رفع البروفايلات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-download"></i> استيراد من MikroTik</h5>
                            </div>
                            <div class="card-body">
                                <p>استيراد البروفايلات الموجودة في سيرفر MikroTik إلى قاعدة البيانات المحلية</p>
                                <form method="POST">
                                    <input type="hidden" name="action" value="sync_from_mikrotik">
                                    <button type="submit" class="btn btn-success" <?= !checkMikroTikConnection() ? 'disabled' : '' ?>>
                                        <i class="fas fa-cloud-download-alt"></i> استيراد البروفايلات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مقارنة البروفايلات -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-dark text-white">
                                <h5><i class="fas fa-database"></i> البروفايلات المحلية</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($local_profiles)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>المدة</th>
                                                    <th>السرعة</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($local_profiles as $profile): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?= htmlspecialchars($profile['name']) ?></strong>
                                                        </td>
                                                        <td><?= formatTime($profile['session_timeout']) ?></td>
                                                        <td>
                                                            <?php if (empty($profile['rate_limit'])): ?>
                                                                <span class="badge bg-info">بدون حد</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning"><?= htmlspecialchars($profile['rate_limit']) ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $exists_in_mikrotik = false;
                                                            foreach ($mikrotik_profiles as $mt_profile) {
                                                                if ($mt_profile['name'] == $profile['name']) {
                                                                    $exists_in_mikrotik = true;
                                                                    break;
                                                                }
                                                            }
                                                            ?>
                                                            <?php if ($exists_in_mikrotik): ?>
                                                                <span class="badge bg-success">متزامن</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning">غير متزامن</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> لا توجد بروفايلات محلية
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h5><i class="fas fa-server"></i> بروفايلات MikroTik</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($mikrotik_profiles)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>المدة</th>
                                                    <th>السرعة</th>
                                                    <th>المستخدمين</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($mikrotik_profiles as $profile): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?= htmlspecialchars($profile['name']) ?></strong>
                                                        </td>
                                                        <td>
                                                            <?= isset($profile['session-timeout']) ? $profile['session-timeout'] : 'غير محدد' ?>
                                                        </td>
                                                        <td>
                                                            <?= isset($profile['rate-limit']) ? htmlspecialchars($profile['rate-limit']) : 'بدون حد' ?>
                                                        </td>
                                                        <td>
                                                            <?= isset($profile['shared-users']) ? $profile['shared-users'] : '1' ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php elseif (checkMikroTikConnection()): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> لا توجد بروفايلات في MikroTik
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle"></i> لا يمكن الاتصال بـ MikroTik
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>المزامنة التلقائية:</h6>
                                        <ul class="list-unstyled">
                                            <li>✅ عند إنشاء بروفايل جديد</li>
                                            <li>✅ عند تحديث بروفايل موجود</li>
                                            <li>✅ عند حذف بروفايل</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6>ملاحظات:</h6>
                                        <ul class="list-unstyled">
                                            <li>• تأكد من تشغيل API في MikroTik</li>
                                            <li>• تحقق من صحة بيانات الاتصال</li>
                                            <li>• النسخ الاحتياطي مهم قبل المزامنة</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/script.js"></script>
</body>
</html>

<?php
// دالة لاستيراد البروفايلات من MikroTik
function importProfilesFromMikroTik() {
    global $conn;
    
    if (!$conn) {
        return false;
    }
    
    $mikrotik_profiles = getMikroTikProfiles();
    if (empty($mikrotik_profiles)) {
        return 0;
    }
    
    $imported_count = 0;
    
    foreach ($mikrotik_profiles as $profile) {
        $name = $profile['name'];
        $display_name = $profile['name']; // استخدام الاسم كاسم معروض افتراضي
        
        // تحويل session-timeout من تنسيق MikroTik إلى ثوان
        $session_timeout = 0;
        if (isset($profile['session-timeout'])) {
            $session_timeout = convertMikroTikTimeToSeconds($profile['session-timeout']);
        }
        
        // تحويل idle-timeout
        $idle_timeout = 0;
        if (isset($profile['idle-timeout'])) {
            $idle_timeout = convertMikroTikTimeToSeconds($profile['idle-timeout']);
        }
        
        $rate_limit = $profile['rate-limit'] ?? '';
        $shared_users = intval($profile['shared-users'] ?? 1);
        
        // التحقق من وجود البروفايل محلياً
        $check_stmt = $conn->prepare("SELECT id FROM hotspot_profiles WHERE name = ?");
        if ($check_stmt) {
            $check_stmt->bind_param('s', $name);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            
            if ($result->num_rows == 0) {
                // البروفايل غير موجود، نضيفه
                $insert_stmt = $conn->prepare("INSERT INTO hotspot_profiles (name, display_name, session_timeout, idle_timeout, rate_limit, shared_users) VALUES (?, ?, ?, ?, ?, ?)");
                if ($insert_stmt) {
                    $insert_stmt->bind_param('ssiisi', $name, $display_name, $session_timeout, $idle_timeout, $rate_limit, $shared_users);
                    if ($insert_stmt->execute()) {
                        $imported_count++;
                    }
                    $insert_stmt->close();
                }
            }
            $check_stmt->close();
        }
    }
    
    return $imported_count;
}

// دالة لتحويل وقت MikroTik إلى ثوان
function convertMikroTikTimeToSeconds($time_str) {
    if (empty($time_str) || $time_str == '00:00:00') {
        return 0;
    }
    
    // تنسيق MikroTik: HH:MM:SS أو أرقام بوحدات مختلفة
    if (preg_match('/^(\d+):(\d+):(\d+)$/', $time_str, $matches)) {
        $hours = intval($matches[1]);
        $minutes = intval($matches[2]);
        $seconds = intval($matches[3]);
        return ($hours * 3600) + ($minutes * 60) + $seconds;
    }
    
    // إذا كان رقم فقط، نعتبره ثوان
    if (is_numeric($time_str)) {
        return intval($time_str);
    }
    
    return 0;
}
?>
