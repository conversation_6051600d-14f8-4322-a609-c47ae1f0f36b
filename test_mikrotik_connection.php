<?php
/**
 * اختبار سريع للاتصال بـ MikroTik بالإعدادات الجديدة
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 اختبار الاتصال بـ MikroTik</h1>";

// تضمين الملفات المطلوبة
require_once 'config.php';

echo "<h2>الإعدادات المحدثة:</h2>";
echo "<ul>";
echo "<li><strong>Host:</strong> " . MIKROTIK_HOST . "</li>";
echo "<li><strong>User:</strong> " . MIKROTIK_USER . "</li>";
echo "<li><strong>Port:</strong> " . MIKROTIK_PORT . "</li>";
echo "<li><strong>Password:</strong> " . (empty(MIKROTIK_PASS) ? 'فارغ' : '***') . "</li>";
echo "</ul>";

// اختبار الاتصال
if (file_exists('mikrotik_api.php')) {
    require_once 'mikrotik_api.php';
    
    echo "<h2>اختبار الاتصال:</h2>";
    
    try {
        $api = new RouterosAPI();
        $api->debug = false;
        
        echo "<div>محاولة الاتصال بـ: " . MIKROTIK_HOST . ":" . MIKROTIK_PORT . "</div>";
        echo "<div>اسم المستخدم: " . MIKROTIK_USER . "</div>";
        
        if ($api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            echo "<div style='color: green; font-size: 18px; font-weight: bold;'>✅ تم الاتصال بـ MikroTik بنجاح!</div>";
            
            // اختبار بعض الأوامر الأساسية
            try {
                echo "<h3>معلومات النظام:</h3>";
                
                // اسم الراوتر
                $identity = $api->comm('/system/identity/print');
                if (!empty($identity)) {
                    echo "<div><strong>اسم الراوتر:</strong> " . $identity[0]['name'] . "</div>";
                }
                
                // معلومات النظام
                $resource = $api->comm('/system/resource/print');
                if (!empty($resource)) {
                    echo "<div><strong>إصدار RouterOS:</strong> " . $resource[0]['version'] . "</div>";
                    echo "<div><strong>نوع الجهاز:</strong> " . $resource[0]['board-name'] . "</div>";
                    echo "<div><strong>وقت التشغيل:</strong> " . $resource[0]['uptime'] . "</div>";
                }
                
                // فحص خدمة API
                echo "<h3>حالة خدمة API:</h3>";
                $api_service = $api->comm('/ip/service/print', array('?name' => 'api'));
                if (!empty($api_service)) {
                    $api_disabled = $api_service[0]['disabled'] ?? 'false';
                    $api_port = $api_service[0]['port'] ?? '8728';
                    
                    if ($api_disabled === 'false') {
                        echo "<div style='color: green;'>✅ خدمة API مفعلة على البورت: " . $api_port . "</div>";
                    } else {
                        echo "<div style='color: red;'>❌ خدمة API معطلة</div>";
                    }
                } else {
                    echo "<div style='color: orange;'>⚠️ لا يمكن الحصول على معلومات خدمة API</div>";
                }
                
                // فحص بروفايلات Hotspot
                echo "<h3>بروفايلات Hotspot:</h3>";
                try {
                    $profiles = $api->comm('/ip/hotspot/user/profile/print');
                    if (!empty($profiles)) {
                        echo "<div style='color: green;'>✅ تم العثور على " . count($profiles) . " بروفايل:</div>";
                        echo "<ul>";
                        foreach ($profiles as $profile) {
                            echo "<li>" . $profile['name'] . "</li>";
                        }
                        echo "</ul>";
                    } else {
                        echo "<div style='color: orange;'>⚠️ لا توجد بروفايلات Hotspot</div>";
                    }
                } catch (Exception $e) {
                    echo "<div style='color: orange;'>⚠️ لا يمكن الوصول إلى بروفايلات Hotspot: " . $e->getMessage() . "</div>";
                }
                
                // اختبار إنشاء مستخدم تجريبي
                echo "<h3>اختبار إنشاء مستخدم:</h3>";
                $test_username = 'TEST' . rand(1000, 9999);
                $test_password = rand(100000, 999999);
                
                try {
                    $api->comm('/ip/hotspot/user/add', array(
                        'name' => $test_username,
                        'password' => $test_password,
                        'profile' => 'default'
                    ));
                    echo "<div style='color: green;'>✅ تم إنشاء مستخدم تجريبي: $test_username</div>";
                    
                    // حذف المستخدم التجريبي
                    $api->comm('/ip/hotspot/user/remove', array('numbers' => $test_username));
                    echo "<div style='color: green;'>✅ تم حذف المستخدم التجريبي</div>";
                    
                } catch (Exception $e) {
                    echo "<div style='color: red;'>❌ فشل في إنشاء المستخدم التجريبي: " . $e->getMessage() . "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ تم الاتصال لكن فشل في تنفيذ بعض الأوامر: " . $e->getMessage() . "</div>";
            }
            
            $api->disconnect();
            
            echo "<hr>";
            echo "<div style='color: green; font-size: 20px; font-weight: bold; text-align: center; padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
            echo "🎉 الاتصال يعمل بشكل مثالي!<br>";
            echo "يمكنك الآن استخدام النظام بشكل طبيعي.";
            echo "</div>";
            
        } else {
            echo "<div style='color: red; font-size: 16px;'>❌ فشل الاتصال بـ MikroTik</div>";
            
            echo "<h3>تشخيص المشكلة:</h3>";
            echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
            echo "<strong>الأسباب المحتملة:</strong>";
            echo "<ol>";
            echo "<li><strong>عنوان IP غير صحيح:</strong> تأكد من أن ********** هو العنوان الصحيح</li>";
            echo "<li><strong>خدمة API معطلة:</strong> فعل خدمة API في MikroTik</li>";
            echo "<li><strong>بيانات الدخول خاطئة:</strong> تحقق من اسم المستخدم وكلمة المرور</li>";
            echo "<li><strong>البورت مغلق:</strong> تأكد من أن البورت 8728 مفتوح</li>";
            echo "<li><strong>جدار الحماية:</strong> قد يحجب الاتصال</li>";
            echo "</ol>";
            echo "</div>";
            
            echo "<h3>خطوات التحقق:</h3>";
            echo "<ol>";
            echo "<li>تأكد من أن الراوتر متاح: <code>ping **********</code></li>";
            echo "<li>تحقق من تفعيل API في MikroTik: <code>/ip service enable api</code></li>";
            echo "<li>تأكد من صحة بيانات الدخول</li>";
            echo "</ol>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div style='color: red;'>❌ ملف mikrotik_api.php غير موجود</div>";
}

echo "<hr>";
echo "<h2>روابط مفيدة:</h2>";
echo "<ul>";
echo "<li><a href='index.php'>العودة إلى النظام الرئيسي</a></li>";
echo "<li><a href='fix_database.php'>إصلاح قاعدة البيانات</a></li>";
echo "<li><a href='debug_connection.php'>تشخيص شامل</a></li>";
echo "</ul>";
?>
