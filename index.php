<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

// المزامنة التلقائية معطلة مؤقتاً لحل مشكلة الأخطاء

// التحقق من الاتصال بقاعدة البيانات
if (!$conn) {
    die("فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error());
}

// معالجة إنشاء كروت جديدة
if ($_POST['action'] == 'generate_cards' && isset($_POST['count'])) {
    $count = intval($_POST['count']);
    $profile = $_POST['profile'] ?? 'default';

    // حساب مدة الصلاحية من الأيام والساعات والدقائق
    $validity_days = intval($_POST['validity_days'] ?? 1);
    $validity_hours = intval($_POST['validity_hours'] ?? 0);
    $validity_minutes = intval($_POST['validity_minutes'] ?? 0);

    // تحويل إلى ساعات للتوافق مع الدالة الموجودة
    $total_hours = ($validity_days * 24) + $validity_hours + ($validity_minutes / 60);

    $generated_cards = generateHotspotCards($count, $profile, $total_hours);

    if ($generated_cards) {
        $_SESSION['success'] = "تم إنشاء {$count} كارت بنجاح";
        $_SESSION['generated_cards'] = $generated_cards;
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء إنشاء الكروت";
    }
}

// جلب الكروت الموجودة
$cards = getAllCards();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة كروت الهوتسبوت - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .card-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .status-active { color: #28a745; }
        .status-used { color: #dc3545; }
        .status-expired { color: #6c757d; }
        .btn-generate {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
        }
        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="header-content">
                    <div class="logo-container">
                        <img src="assets/img/Logo.png" alt="MikroSys Logo" class="system-logo">
                    </div>
                    <div class="header-text">
                        <h1><i class="fas fa-wifi"></i> نظام إدارة كروت الهوتسبوت</h1>
                        <p class="mb-0">MikroSys - إدارة شبكة المايكروتك</p>
                    </div>
                    <div class="header-actions">
                        <a href="profiles.php" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-tachometer-alt"></i> البروفايلات
                        </a>
                        <a href="settings.php" class="btn btn-light btn-sm">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="container p-4">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i> <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle"></i> <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <div class="row">
                    <!-- نموذج إنشاء كروت جديدة -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-plus-circle"></i> إنشاء كروت جديدة</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="generate-cards-form">
                                    <input type="hidden" name="action" value="generate_cards">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">عدد الكروت</label>
                                        <input type="number" name="count" class="form-control" min="1" max="100" value="10" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">البروفايل</label>
                                        <select name="profile" class="form-select">
                                            <?php
                                            $profiles = getAvailableProfiles();
                                            foreach ($profiles as $profile):
                                            ?>
                                                <option value="<?= $profile['name'] ?>"
                                                    <?= $profile['name'] == '24hours' ? 'selected' : '' ?>>
                                                    <?= $profile['display_name'] ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">مدة الصلاحية</label>
                                        <div class="row">
                                            <div class="col-4">
                                                <label class="form-label small">الأيام</label>
                                                <input type="number" name="validity_days" class="form-control" value="1" min="0" max="365">
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label small">الساعات</label>
                                                <input type="number" name="validity_hours" class="form-control" value="0" min="0" max="23">
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label small">الدقائق</label>
                                                <input type="number" name="validity_minutes" class="form-control" value="0" min="0" max="59">
                                            </div>
                                        </div>
                                        <div class="form-text">
                                            <small id="validity-preview">المدة الإجمالية: 1 يوم</small>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-generate w-100">
                                        <i class="fas fa-magic"></i> إنشاء الكروت
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات -->
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card text-center bg-success text-white">
                                    <div class="card-body">
                                        <h3 id="active-cards"><?= getCardCount('active') ?></h3>
                                        <p>كروت نشطة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center bg-danger text-white">
                                    <div class="card-body">
                                        <h3 id="used-cards"><?= getCardCount('used') ?></h3>
                                        <p>كروت مستخدمة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center bg-warning text-white">
                                    <div class="card-body">
                                        <h3 id="expired-cards"><?= getCardCount('expired') ?></h3>
                                        <p>كروت منتهية</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center bg-info text-white">
                                    <div class="card-body">
                                        <h3 id="total-cards"><?= getCardCount('all') ?></h3>
                                        <p>إجمالي الكروت</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الكروت المنشأة حديثاً -->
                        <?php if (isset($_SESSION['generated_cards'])): ?>
                            <div class="card mt-3">
                                <div class="card-header bg-success text-white">
                                    <h5><i class="fas fa-ticket-alt"></i> الكروت المنشأة حديثاً</h5>
                                    <div class="float-end">
                                        <a href="print_cards.php" class="btn btn-light btn-sm" target="_blank">
                                            <i class="fas fa-print"></i> طباعة عادية
                                        </a>
                                        <a href="print_cards_advanced.php" class="btn btn-outline-light btn-sm" target="_blank">
                                            <i class="fas fa-cog"></i> طباعة متقدمة
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                                    <?php foreach ($_SESSION['generated_cards'] as $card): ?>
                                        <div class="card-item">
                                            <strong>اسم المستخدم:</strong> <?= $card['username'] ?><br>
                                            <strong>كلمة المرور:</strong> <?= $card['password'] ?><br>
                                            <strong>البروفايل:</strong> <?= $card['profile'] ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php unset($_SESSION['generated_cards']); ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- جدول الكروت -->
                <div class="card mt-4">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-list"></i> جميع الكروت</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>كلمة المرور</th>
                                        <th>البروفايل</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cards as $card): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($card['username']) ?></td>
                                            <td><?= htmlspecialchars($card['password']) ?></td>
                                            <td><?= htmlspecialchars($card['profile']) ?></td>
                                            <td>
                                                <span class="status-<?= $card['status'] ?>">
                                                    <?= getStatusText($card['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= date('Y-m-d H:i', strtotime($card['created_at'])) ?></td>
                                            <td><?= date('Y-m-d H:i', strtotime($card['expires_at'])) ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-danger" onclick="deleteCard(<?= $card['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/script.js"></script>
    <script>
        function deleteCard(cardId) {
            if (confirm('هل أنت متأكد من حذف هذا الكارت؟')) {
                window.location.href = 'delete_card.php?id=' + cardId;
            }
        }

        // حساب المدة الإجمالية
        function updateValidityPreview() {
            const days = parseInt(document.querySelector('input[name="validity_days"]').value) || 0;
            const hours = parseInt(document.querySelector('input[name="validity_hours"]').value) || 0;
            const minutes = parseInt(document.querySelector('input[name="validity_minutes"]').value) || 0;

            let preview = '';
            const parts = [];

            if (days > 0) parts.push(days + ' يوم');
            if (hours > 0) parts.push(hours + ' ساعة');
            if (minutes > 0) parts.push(minutes + ' دقيقة');

            if (parts.length === 0) {
                preview = 'يرجى تحديد مدة صالحة';
            } else {
                preview = 'المدة الإجمالية: ' + parts.join(' و ');
            }

            document.getElementById('validity-preview').textContent = preview;
        }

        // تحديث المعاينة عند تحميل الصفحة وعند تغيير القيم
        document.addEventListener('DOMContentLoaded', function() {
            updateValidityPreview();

            ['validity_days', 'validity_hours', 'validity_minutes'].forEach(name => {
                const input = document.querySelector(`input[name="${name}"]`);
                if (input) {
                    input.addEventListener('input', updateValidityPreview);
                }
            });
        });
    </script>
</body>
</html>
