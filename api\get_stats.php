<?php
/**
 * API للحصول على إحصائيات النظام
 * يعيد البيانات بصيغة JSON
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config.php';
require_once '../functions.php';

try {
    // التحقق من الاتصال بقاعدة البيانات
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    // الحصول على الإحصائيات
    $stats = getSystemStats();
    
    // إضافة إحصائيات إضافية
    $additional_stats = [];
    
    // الكروت المنشأة خلال آخر 24 ساعة
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM hotspot_cards WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $additional_stats['cards_last_24h'] = $row['count'];
    }
    
    // الكروت المنشأة خلال آخر 7 أيام
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM hotspot_cards WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $additional_stats['cards_last_7d'] = $row['count'];
    }
    
    // الكروت المستخدمة اليوم
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM hotspot_cards WHERE DATE(used_at) = CURDATE()");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $additional_stats['used_today'] = $row['count'];
    }
    
    // متوسط الكروت المنشأة يومياً (آخر 30 يوم)
    $result = mysqli_query($conn, "SELECT AVG(daily_count) as avg_daily FROM (
        SELECT DATE(created_at) as date, COUNT(*) as daily_count 
        FROM hotspot_cards 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
    ) as daily_stats");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $additional_stats['avg_daily_cards'] = round($row['avg_daily'] ?? 0, 1);
    }
    
    // إحصائيات البروفايلات
    $result = mysqli_query($conn, "SELECT profile, COUNT(*) as count FROM hotspot_cards GROUP BY profile ORDER BY count DESC");
    $profile_stats = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $profile_stats[] = [
                'profile' => $row['profile'],
                'count' => $row['count']
            ];
        }
    }
    $additional_stats['profile_stats'] = $profile_stats;
    
    // حالة الاتصال بـ MikroTik
    $additional_stats['mikrotik_connected'] = checkMikroTikConnection();
    
    // معلومات النظام
    $additional_stats['system_info'] = [
        'php_version' => PHP_VERSION,
        'mysql_version' => mysqli_get_server_info($conn),
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
        'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . ' MB'
    ];
    
    // دمج الإحصائيات
    $all_stats = array_merge($stats, $additional_stats);
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'stats' => $all_stats,
        'timestamp' => time(),
        'formatted_time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // في حالة حدوث خطأ
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    
    // تسجيل الخطأ
    logError("API Error: " . $e->getMessage());
}

// إغلاق الاتصال
if ($conn) {
    mysqli_close($conn);
}
?>
