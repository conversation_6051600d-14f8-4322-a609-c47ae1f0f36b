<?php
/**
 * ملف تنظيف الكروت المنتهية الصلاحية
 * يمكن تشغيله يدوياً أو عبر Cron Job
 */

require_once 'config.php';
require_once 'functions.php';

// التحقق من طريقة التشغيل
$is_cli = php_sapi_name() === 'cli';
$is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';

if (!$is_cli && !$is_ajax) {
    // إذا تم الوصول عبر المتصفح مباشرة
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
        <meta charset='UTF-8'>
        <title>تنظيف الكروت المنتهية</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .success { color: green; }
            .error { color: red; }
            .info { color: blue; }
        </style>
    </head>
    <body>";
}

function output($message, $type = 'info') {
    global $is_cli, $is_ajax;
    
    if ($is_cli) {
        echo "[" . date('Y-m-d H:i:s') . "] $message\n";
    } elseif ($is_ajax) {
        echo json_encode(['message' => $message, 'type' => $type]);
    } else {
        echo "<div class='$type'>[" . date('Y-m-d H:i:s') . "] $message</div>";
    }
}

try {
    output("بدء عملية تنظيف الكروت المنتهية الصلاحية...", 'info');
    
    // الحصول على الكروت المنتهية الصلاحية
    $expired_cards_query = "SELECT id, username FROM hotspot_cards 
                           WHERE status = 'active' AND expires_at < NOW()";
    $result = mysqli_query($conn, $expired_cards_query);
    
    if (!$result) {
        throw new Exception("خطأ في استعلام قاعدة البيانات: " . mysqli_error($conn));
    }
    
    $expired_count = mysqli_num_rows($result);
    output("تم العثور على $expired_count كارت منتهي الصلاحية", 'info');
    
    if ($expired_count > 0) {
        $deleted_from_mikrotik = 0;
        $updated_in_db = 0;
        
        // تحديث حالة الكروت في قاعدة البيانات
        $update_query = "UPDATE hotspot_cards SET status = 'expired' 
                        WHERE status = 'active' AND expires_at < NOW()";
        
        if (mysqli_query($conn, $update_query)) {
            $updated_in_db = mysqli_affected_rows($conn);
            output("تم تحديث حالة $updated_in_db كارت في قاعدة البيانات", 'success');
        } else {
            throw new Exception("فشل تحديث قاعدة البيانات: " . mysqli_error($conn));
        }
        
        // حذف المستخدمين من MikroTik
        mysqli_data_seek($result, 0); // إعادة تعيين مؤشر النتائج
        while ($row = mysqli_fetch_assoc($result)) {
            $username = $row['username'];
            
            if (deleteMikroTikUser($username)) {
                $deleted_from_mikrotik++;
                output("تم حذف المستخدم $username من MikroTik", 'success');
            } else {
                output("فشل حذف المستخدم $username من MikroTik", 'error');
            }
            
            // تأخير قصير لتجنب إرهاق الخادم
            usleep(100000); // 0.1 ثانية
        }
        
        output("تم حذف $deleted_from_mikrotik مستخدم من MikroTik", 'success');
    }
    
    // إحصائيات إضافية
    $stats = getSystemStats();
    output("الإحصائيات الحالية:", 'info');
    output("- إجمالي الكروت: {$stats['total_cards']}", 'info');
    output("- كروت نشطة: {$stats['active_cards']}", 'info');
    output("- كروت مستخدمة: {$stats['used_cards']}", 'info');
    output("- كروت منتهية: {$stats['expired_cards']}", 'info');
    
    // حذف الكروت القديمة جداً (اختياري)
    $cleanup_old = isset($_GET['cleanup_old']) ? $_GET['cleanup_old'] : false;
    if ($cleanup_old) {
        output("بدء حذف الكروت القديمة (أكثر من 30 يوم)...", 'info');
        
        $old_cards_query = "SELECT id, username FROM hotspot_cards 
                           WHERE status IN ('expired', 'used') 
                           AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $old_result = mysqli_query($conn, $old_cards_query);
        
        if ($old_result) {
            $old_count = mysqli_num_rows($old_result);
            output("تم العثور على $old_count كارت قديم للحذف", 'info');
            
            if ($old_count > 0) {
                // حذف من MikroTik أولاً
                while ($row = mysqli_fetch_assoc($old_result)) {
                    deleteMikroTikUser($row['username']);
                }
                
                // حذف من قاعدة البيانات
                $delete_old_query = "DELETE FROM hotspot_cards 
                                   WHERE status IN ('expired', 'used') 
                                   AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
                
                if (mysqli_query($conn, $delete_old_query)) {
                    $deleted_old = mysqli_affected_rows($conn);
                    output("تم حذف $deleted_old كارت قديم", 'success');
                } else {
                    output("فشل حذف الكروت القديمة: " . mysqli_error($conn), 'error');
                }
            }
        }
    }
    
    output("تمت عملية التنظيف بنجاح!", 'success');
    
} catch (Exception $e) {
    output("خطأ: " . $e->getMessage(), 'error');
    logError("Cleanup error: " . $e->getMessage());
}

if (!$is_cli && !$is_ajax) {
    echo "<br><br>";
    echo "<a href='index.php'>العودة للصفحة الرئيسية</a> | ";
    echo "<a href='cleanup.php?cleanup_old=1'>تنظيف الكروت القديمة أيضاً</a>";
    echo "</body></html>";
}

// إغلاق الاتصال
if ($conn) {
    mysqli_close($conn);
}
?>
