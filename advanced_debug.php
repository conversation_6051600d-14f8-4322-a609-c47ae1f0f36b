<?php
/**
 * تشخيص متقدم لمشكلة API
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 تشخيص متقدم لـ API</h1>";

require_once 'config.php';

echo "<h2>1. اختبار اتصال خام بـ API:</h2>";

function testRawConnection($host, $port, $user, $pass) {
    echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
    echo "<strong>اختبار اتصال خام...</strong><br>";
    
    $socket = @fsockopen($host, $port, $errno, $errstr, 10);
    if (!$socket) {
        echo "<div style='color: red;'>❌ فشل في فتح Socket: $errstr ($errno)</div>";
        echo "</div>";
        return false;
    }
    
    echo "<div style='color: green;'>✅ تم فتح Socket بنجاح</div>";
    
    // إرسال أمر login
    $login_command = "/login\n";
    fwrite($socket, chr(strlen($login_command)) . $login_command);
    echo "<div>📤 تم إرسال: " . trim($login_command) . "</div>";
    
    // قراءة الرد
    $response = '';
    $start_time = time();
    while (time() - $start_time < 5) { // انتظار 5 ثواني
        $data = fread($socket, 1024);
        if ($data) {
            $response .= $data;
            break;
        }
        usleep(100000); // انتظار 0.1 ثانية
    }
    
    if ($response) {
        echo "<div style='color: green;'>📥 تم استلام رد:</div>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
        echo "Raw data: " . bin2hex($response) . "\n";
        echo "Text: " . htmlspecialchars($response);
        echo "</pre>";
    } else {
        echo "<div style='color: red;'>❌ لم يتم استلام أي رد من الراوتر</div>";
    }
    
    fclose($socket);
    echo "</div>";
    
    return !empty($response);
}

// اختبار الاتصال الخام
$raw_result = testRawConnection(MIKROTIK_HOST, MIKROTIK_PORT, MIKROTIK_USER, MIKROTIK_PASS);

echo "<h2>2. اختبار مع مهلة زمنية أطول:</h2>";

if (file_exists('mikrotik_api.php')) {
    require_once 'mikrotik_api.php';
    
    class ExtendedRouterosAPI extends RouterosAPI {
        public function __construct() {
            parent::__construct();
            $this->timeout = 10; // زيادة المهلة الزمنية
        }
        
        public function debug_print($str) {
            echo "<div style='font-family: monospace; font-size: 12px; color: #666;'>" . htmlspecialchars($str) . "</div>";
        }
    }
    
    try {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
        echo "<strong>اختبار مع API محسن:</strong><br>";
        
        $api = new ExtendedRouterosAPI();
        $api->debug = true;
        
        echo "<div>محاولة الاتصال مع مهلة زمنية 10 ثواني...</div>";
        
        if ($api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
            echo "<div style='color: green; font-weight: bold;'>✅ نجح الاتصال!</div>";
            $api->disconnect();
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ فشل الاتصال</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>خطأ: " . $e->getMessage() . "</div>";
        echo "</div>";
    }
}

echo "<h2>3. اختبار Telnet يدوي:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
echo "<strong>جرب هذا الأمر في Command Prompt:</strong><br>";
echo "<code>telnet " . MIKROTIK_HOST . " " . MIKROTIK_PORT . "</code><br><br>";
echo "إذا اتصل بنجاح، ستظهر رسالة ترحيب من MikroTik.<br>";
echo "إذا لم يتصل، فهناك مشكلة في إعدادات الراوتر.";
echo "</div>";

echo "<h2>4. فحص إعدادات MikroTik المطلوبة:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<strong>تشغيل هذه الأوامر في MikroTik:</strong><br><br>";

echo "<strong>أ) فحص خدمة API:</strong><br>";
echo "<code>/ip service print</code><br>";
echo "تأكد من أن API غير معطل (لا يوجد X أمامه)<br><br>";

echo "<strong>ب) تفعيل API إذا كان معطل:</strong><br>";
echo "<code>/ip service set api disabled=no</code><br><br>";

echo "<strong>ج) فحص المستخدمين:</strong><br>";
echo "<code>/user print</code><br>";
echo "تأكد من وجود المستخدم mohager<br><br>";

echo "<strong>د) إنشاء/تحديث المستخدم:</strong><br>";
echo "<code>/user add name=mohager password=\"P@\$\$w0rd\" group=full</code><br>";
echo "أو إذا كان موجود:<br>";
echo "<code>/user set mohager group=full</code><br><br>";

echo "<strong>هـ) فحص عنوان IP:</strong><br>";
echo "<code>/ip address print</code><br>";
echo "تأكد من أن ********** هو العنوان الصحيح<br><br>";

echo "<strong>و) اختبار من MikroTik نفسه:</strong><br>";
echo "<code>/tool fetch url=\"http://**********\" mode=http</code><br>";
echo "للتأكد من أن الشبكة تعمل";
echo "</div>";

echo "<h2>5. حلول بديلة:</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
echo "<strong>إذا استمرت المشكلة، جرب:</strong><br>";
echo "<ol>";
echo "<li><strong>إعادة تشغيل خدمة API:</strong><br>";
echo "<code>/ip service disable api</code><br>";
echo "<code>/ip service enable api</code></li>";
echo "<li><strong>تغيير بورت API:</strong><br>";
echo "<code>/ip service set api port=8729</code><br>";
echo "ثم تحديث MIKROTIK_PORT في config.php</li>";
echo "<li><strong>استخدام API-SSL:</strong><br>";
echo "<code>/ip service enable api-ssl</code><br>";
echo "واستخدام البورت 8729</li>";
echo "<li><strong>إعادة تشغيل الراوتر:</strong><br>";
echo "<code>/system reboot</code></li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<div style='text-align: center; padding: 20px;'>";
echo "<strong>بعد تطبيق الحلول، اختبر الاتصال مرة أخرى:</strong><br>";
echo "<a href='detailed_debug.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إعادة التشخيص</a>";
echo "<a href='test_mikrotik_connection.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الاتصال</a>";
echo "</div>";
?>
