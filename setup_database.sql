-- ملف إعد<PERSON> قاعدة البيانات لنظام MikroSys
-- يجب تشغيل هذا الملف بصلاحيات root في MySQL

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `mikrosys` CHARACTER SET utf8 COLLATE utf8_general_ci;

-- إن<PERSON>اء المستخدم
CREATE USER IF NOT EXISTS 'mikrosys'@'localhost' IDENTIFIED BY 'mikrosys@2025';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON `mikrosys`.* TO 'mikrosys'@'localhost';

-- تطبيق التغييرات
FLUSH PRIVILEGES;

-- استخدام قاعدة البيانات
USE `mikrosys`;

-- إنشاء جدول الكروت
CREATE TABLE IF NOT EXISTS `hotspot_cards` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(50) UNIQUE NOT NULL,
    `password` VARCHAR(50) NOT NULL,
    `profile` VARCHAR(50) NOT NULL,
    `status` ENUM('active', 'used', 'expired') DEFAULT 'active',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL,
    `used_at` TIMESTAMP NULL,
    `mikrotik_id` VARCHAR(50) NULL,
    `notes` TEXT NULL,
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- إنشاء جدول البروفايلات
CREATE TABLE IF NOT EXISTS `hotspot_profiles` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) UNIQUE NOT NULL,
    `display_name` VARCHAR(100) NOT NULL,
    `session_timeout` INT DEFAULT 0,
    `idle_timeout` INT DEFAULT 0,
    `rate_limit` VARCHAR(50) DEFAULT '',
    `shared_users` INT DEFAULT 1,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- إنشاء جدول الإعدادات
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) UNIQUE NOT NULL,
    `setting_value` TEXT,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- إدراج البروفايلات الافتراضية
INSERT IGNORE INTO `hotspot_profiles` (`name`, `display_name`, `session_timeout`, `idle_timeout`, `rate_limit`, `shared_users`) VALUES
('1hour', 'ساعة واحدة', 3600, 300, '1M/1M', 1),
('3hours', '3 ساعات', 10800, 300, '2M/2M', 1),
('6hours', '6 ساعات', 21600, 600, '3M/3M', 1),
('12hours', '12 ساعة', 43200, 600, '5M/5M', 1),
('24hours', '24 ساعة', 86400, 900, '10M/10M', 1),
('7days', 'أسبوع', 604800, 1800, '15M/15M', 2),
('30days', 'شهر', 2592000, 3600, '20M/20M', 3);

-- إدراج إعدادات افتراضية
INSERT IGNORE INTO `system_settings` (`setting_key`, `setting_value`) VALUES
('system_installed', '1'),
('installation_date', NOW()),
('default_profile', '24hours'),
('cards_per_page', '50'),
('auto_cleanup', '1'),
('cleanup_interval', '24');

-- عرض النتائج
SELECT 'Database setup completed successfully!' as Status;
SELECT COUNT(*) as 'Profiles Created' FROM `hotspot_profiles`;
SELECT COUNT(*) as 'Settings Created' FROM `system_settings`;
