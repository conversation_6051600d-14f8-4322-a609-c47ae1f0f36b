<?php
/**
 * اختبار الاتصالات - قاعدة البيانات و MikroTik
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 اختبار الاتصالات</h1>";

// 1. اختبار الاتصال بقاعدة البيانات
echo "<h2>1. اختبار قاعدة البيانات</h2>";

// اختبار الاتصال بـ root أولاً
echo "<h3>أ) اختبار الاتصال بـ root:</h3>";
try {
    $root_conn = new mysqli('localhost', 'root', '');
    if ($root_conn->connect_error) {
        echo "<div style='color: red;'>❌ فشل الاتصال بـ root: " . $root_conn->connect_error . "</div>";
    } else {
        echo "<div style='color: green;'>✅ تم الاتصال بـ root بنجاح</div>";
        
        // فحص وجود المستخدم mikrosys
        $result = $root_conn->query("SELECT User, Host FROM mysql.user WHERE User = 'mikrosys'");
        if ($result && $result->num_rows > 0) {
            echo "<div style='color: green;'>✅ المستخدم mikrosys موجود</div>";
            while ($row = $result->fetch_assoc()) {
                echo "<div>  - User: {$row['User']}, Host: {$row['Host']}</div>";
            }
        } else {
            echo "<div style='color: red;'>❌ المستخدم mikrosys غير موجود</div>";
            
            // إنشاء المستخدم
            echo "<div>محاولة إنشاء المستخدم...</div>";
            $create_user = "CREATE USER 'mikrosys'@'localhost' IDENTIFIED BY 'mikrosys@2025'";
            if ($root_conn->query($create_user)) {
                echo "<div style='color: green;'>✅ تم إنشاء المستخدم mikrosys</div>";
                
                // منح الصلاحيات
                $grant_privileges = "GRANT ALL PRIVILEGES ON mikrosys.* TO 'mikrosys'@'localhost'";
                if ($root_conn->query($grant_privileges)) {
                    echo "<div style='color: green;'>✅ تم منح الصلاحيات</div>";
                    $root_conn->query("FLUSH PRIVILEGES");
                } else {
                    echo "<div style='color: red;'>❌ فشل في منح الصلاحيات: " . $root_conn->error . "</div>";
                }
            } else {
                echo "<div style='color: red;'>❌ فشل في إنشاء المستخدم: " . $root_conn->error . "</div>";
            }
        }
        
        // فحص وجود قاعدة البيانات
        $result = $root_conn->query("SHOW DATABASES LIKE 'mikrosys'");
        if ($result && $result->num_rows > 0) {
            echo "<div style='color: green;'>✅ قاعدة البيانات mikrosys موجودة</div>";
        } else {
            echo "<div style='color: red;'>❌ قاعدة البيانات mikrosys غير موجودة</div>";
            
            // إنشاء قاعدة البيانات
            echo "<div>محاولة إنشاء قاعدة البيانات...</div>";
            $create_db = "CREATE DATABASE mikrosys CHARACTER SET utf8 COLLATE utf8_general_ci";
            if ($root_conn->query($create_db)) {
                echo "<div style='color: green;'>✅ تم إنشاء قاعدة البيانات mikrosys</div>";
            } else {
                echo "<div style='color: red;'>❌ فشل في إنشاء قاعدة البيانات: " . $root_conn->error . "</div>";
            }
        }
        
        $root_conn->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
}

// اختبار الاتصال بالمستخدم mikrosys
echo "<h3>ب) اختبار الاتصال بـ mikrosys:</h3>";
try {
    $mikrosys_conn = new mysqli('localhost', 'mikrosys', 'mikrosys@2025', 'mikrosys');
    if ($mikrosys_conn->connect_error) {
        echo "<div style='color: red;'>❌ فشل الاتصال بـ mikrosys: " . $mikrosys_conn->connect_error . "</div>";
    } else {
        echo "<div style='color: green;'>✅ تم الاتصال بـ mikrosys بنجاح</div>";
        
        // فحص الجداول
        $result = $mikrosys_conn->query("SHOW TABLES");
        if ($result && $result->num_rows > 0) {
            echo "<div style='color: green;'>✅ الجداول موجودة:</div>";
            while ($row = $result->fetch_array()) {
                echo "<div>  - {$row[0]}</div>";
            }
        } else {
            echo "<div style='color: orange;'>⚠️ لا توجد جداول - يجب تشغيل التثبيت</div>";
        }
        
        $mikrosys_conn->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
}

// 2. اختبار الاتصال بـ MikroTik
echo "<h2>2. اختبار الاتصال بـ MikroTik</h2>";

// قراءة الإعدادات من config.php
if (file_exists('config.php')) {
    require_once 'config.php';
    
    echo "<h3>إعدادات MikroTik الحالية:</h3>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . (defined('MIKROTIK_HOST') ? MIKROTIK_HOST : 'غير محدد') . "</li>";
    echo "<li><strong>User:</strong> " . (defined('MIKROTIK_USER') ? MIKROTIK_USER : 'غير محدد') . "</li>";
    echo "<li><strong>Port:</strong> " . (defined('MIKROTIK_PORT') ? MIKROTIK_PORT : 'غير محدد') . "</li>";
    echo "<li><strong>Password:</strong> " . (defined('MIKROTIK_PASS') && !empty(MIKROTIK_PASS) ? '***' : 'فارغ') . "</li>";
    echo "</ul>";
    
    // اختبار الاتصال
    if (file_exists('mikrotik_api.php')) {
        require_once 'mikrotik_api.php';
        
        echo "<h3>اختبار الاتصال:</h3>";
        try {
            $api = new RouterosAPI();
            $api->debug = false;
            
            if ($api->connect(MIKROTIK_HOST, MIKROTIK_USER, MIKROTIK_PASS, MIKROTIK_PORT)) {
                echo "<div style='color: green;'>✅ تم الاتصال بـ MikroTik بنجاح!</div>";
                
                // اختبار بعض الأوامر
                try {
                    $identity = $api->comm('/system/identity/print');
                    if (!empty($identity)) {
                        echo "<div><strong>اسم الراوتر:</strong> " . $identity[0]['name'] . "</div>";
                    }
                    
                    $resource = $api->comm('/system/resource/print');
                    if (!empty($resource)) {
                        echo "<div><strong>إصدار RouterOS:</strong> " . $resource[0]['version'] . "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div style='color: orange;'>⚠️ تم الاتصال لكن فشل في تنفيذ الأوامر: " . $e->getMessage() . "</div>";
                }
                
                $api->disconnect();
            } else {
                echo "<div style='color: red;'>❌ فشل الاتصال بـ MikroTik</div>";
                echo "<div>تحقق من:</div>";
                echo "<ul>";
                echo "<li>عنوان IP صحيح ومتاح</li>";
                echo "<li>API مفعل في MikroTik</li>";
                echo "<li>اسم المستخدم وكلمة المرور صحيحة</li>";
                echo "<li>البورت 8728 مفتوح</li>";
                echo "</ul>";
            }
        } catch (Exception $e) {
            echo "<div style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ ملف mikrotik_api.php غير موجود</div>";
    }
} else {
    echo "<div style='color: red;'>❌ ملف config.php غير موجود</div>";
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li>إذا كانت قاعدة البيانات تعمل، تأكد من تشغيل install.php لإنشاء الجداول</li>";
echo "<li>تحقق من إعدادات MikroTik في config.php</li>";
echo "<li>تأكد من أن API مفعل في MikroTik</li>";
echo "<li>تأكد من أن عنوان IP صحيح ومتاح</li>";
echo "</ol>";
?>
