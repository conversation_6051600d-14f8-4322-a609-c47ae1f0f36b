<?php
/**
 * اختبار ميزات البروفايلات والمدة المخصصة
 */

session_start();
require_once 'config.php';
require_once 'functions.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البروفايلات والمدة المخصصة - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 اختبار البروفايلات والمدة المخصصة</h1>
        
        <!-- اختبار قاعدة البيانات -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5>1. اختبار قاعدة البيانات</h5>
            </div>
            <div class="card-body">
                <?php if ($conn): ?>
                    <div class="alert alert-success">
                        ✅ الاتصال بقاعدة البيانات نجح
                    </div>
                    
                    <!-- اختبار جدول البروفايلات -->
                    <?php
                    $result = $conn->query("SHOW TABLES LIKE 'hotspot_profiles'");
                    if ($result && $result->num_rows > 0):
                    ?>
                        <div class="alert alert-success">
                            ✅ جدول hotspot_profiles موجود
                        </div>
                        
                        <!-- عرض البروفايلات الموجودة -->
                        <?php
                        $profiles = getAvailableProfiles();
                        if (!empty($profiles)):
                        ?>
                            <h6>البروفايلات الموجودة:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>الاسم المعروض</th>
                                            <th>مدة الجلسة</th>
                                            <th>حد السرعة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($profiles as $profile): ?>
                                            <tr>
                                                <td><code><?= htmlspecialchars($profile['name']) ?></code></td>
                                                <td><?= htmlspecialchars($profile['display_name']) ?></td>
                                                <td><?= formatTime($profile['session_timeout']) ?></td>
                                                <td>
                                                    <?php if (empty($profile['rate_limit'])): ?>
                                                        <span class="badge bg-info">بدون حد</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning"><?= htmlspecialchars($profile['rate_limit']) ?></span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                ⚠️ لا توجد بروفايلات في قاعدة البيانات
                            </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="alert alert-danger">
                            ❌ جدول hotspot_profiles غير موجود
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="alert alert-danger">
                        ❌ فشل الاتصال بقاعدة البيانات
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- اختبار الدوال الجديدة -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5>2. اختبار الدوال الجديدة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>دالة formatTime:</h6>
                        <ul class="list-unstyled">
                            <li>3600 ثانية = <?= formatTime(3600) ?></li>
                            <li>7200 ثانية = <?= formatTime(7200) ?></li>
                            <li>86400 ثانية = <?= formatTime(86400) ?></li>
                            <li>0 ثانية = <?= formatTime(0) ?></li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>دالة timeToSeconds:</h6>
                        <ul class="list-unstyled">
                            <li>1 يوم = <?= timeToSeconds(1, 0, 0) ?> ثانية</li>
                            <li>2 ساعة = <?= timeToSeconds(0, 2, 0) ?> ثانية</li>
                            <li>30 دقيقة = <?= timeToSeconds(0, 0, 30) ?> ثانية</li>
                            <li>1 يوم و 2 ساعة = <?= timeToSeconds(1, 2, 0) ?> ثانية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار قوالب السرعة -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5>3. قوالب السرعة المتاحة</h5>
            </div>
            <div class="card-body">
                <?php $speedTemplates = getSpeedTemplates(); ?>
                <div class="row">
                    <?php foreach ($speedTemplates as $speed => $description): ?>
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-primary"><?= $speed ?></span>
                            <small class="text-muted"><?= $description ?></small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار قوالب الوقت -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-white">
                <h5>4. قوالب الوقت المتاحة</h5>
            </div>
            <div class="card-body">
                <?php $timeTemplates = getTimeTemplates(); ?>
                <div class="row">
                    <?php foreach ($timeTemplates as $seconds => $description): ?>
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-secondary"><?= $seconds ?> ثانية</span>
                            <small class="text-muted"><?= $description ?></small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار حاسبة المدة -->
        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                <h5>5. اختبار حاسبة المدة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">الأيام</label>
                        <input type="number" id="test_days" class="form-control" value="1" min="0">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الساعات</label>
                        <input type="number" id="test_hours" class="form-control" value="2" min="0" max="23">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الدقائق</label>
                        <input type="number" id="test_minutes" class="form-control" value="30" min="0" max="59">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">النتيجة</label>
                        <input type="text" id="test_result" class="form-control" readonly>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="calculateTestTime()">احسب</button>
                    <div id="test_preview" class="mt-2 text-muted"></div>
                </div>
            </div>
        </div>
        
        <!-- اختبار الصفحات -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5>6. اختبار الصفحات الجديدة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>صفحات البروفايلات:</h6>
                        <ul class="list-unstyled">
                            <li>
                                <a href="profiles.php" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-tachometer-alt"></i> إدارة البروفايلات
                                </a>
                            </li>
                            <?php if (!empty($profiles)): ?>
                                <li class="mt-2">
                                    <a href="edit_profile.php?id=<?= $profiles[0]['id'] ?>" target="_blank" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-edit"></i> تحرير بروفايل
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>الصفحة الرئيسية:</h6>
                        <ul class="list-unstyled">
                            <li>
                                <a href="index.php" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                            </li>
                            <li class="mt-2">
                                <small class="text-muted">تحقق من خيار المدة المخصصة</small>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>ملفات أخرى:</h6>
                        <ul class="list-unstyled">
                            <li>
                                <a href="test_logo.php" target="_blank" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-image"></i> اختبار اللوجو
                                </a>
                            </li>
                            <li class="mt-2">
                                <a href="test_system.php" target="_blank" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-cogs"></i> اختبار النظام
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- خلاصة الاختبار -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5>7. خلاصة الاختبار</h5>
            </div>
            <div class="card-body">
                <?php
                $issues = [];
                
                if (!$conn) {
                    $issues[] = "مشكلة في الاتصال بقاعدة البيانات";
                }
                
                $result = $conn ? $conn->query("SHOW TABLES LIKE 'hotspot_profiles'") : null;
                if (!$result || $result->num_rows == 0) {
                    $issues[] = "جدول البروفايلات غير موجود";
                }
                
                if (!function_exists('formatTime')) {
                    $issues[] = "دالة formatTime غير موجودة";
                }
                
                if (!function_exists('timeToSeconds')) {
                    $issues[] = "دالة timeToSeconds غير موجودة";
                }
                
                if (!file_exists('profiles.php')) {
                    $issues[] = "صفحة البروفايلات غير موجودة";
                }
                
                if (!file_exists('edit_profile.php')) {
                    $issues[] = "صفحة تحرير البروفايل غير موجودة";
                }
                
                if (empty($issues)): ?>
                    <div class="alert alert-success">
                        <h5>🎉 جميع الاختبارات نجحت!</h5>
                        <p>الميزات الجديدة جاهزة للاستخدام:</p>
                        <ul>
                            <li>✅ إدارة البروفايلات</li>
                            <li>✅ تحديد مدة الكارت المخصصة</li>
                            <li>✅ حاسبة الوقت</li>
                            <li>✅ قوالب السرعة</li>
                        </ul>
                        <div class="text-center mt-3">
                            <a href="index.php" class="btn btn-success">🚀 جرب النظام الآن</a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <h5>⚠️ توجد بعض المشاكل:</h5>
                        <ul>
                            <?php foreach ($issues as $issue): ?>
                                <li><?= $issue ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <p><strong>الحل:</strong> تأكد من رفع جميع الملفات وإعداد قاعدة البيانات.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">تم إنشاء هذا التقرير في: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateTestTime() {
            const days = parseInt(document.getElementById('test_days').value) || 0;
            const hours = parseInt(document.getElementById('test_hours').value) || 0;
            const minutes = parseInt(document.getElementById('test_minutes').value) || 0;
            
            const totalSeconds = (days * 24 * 60 * 60) + (hours * 60 * 60) + (minutes * 60);
            document.getElementById('test_result').value = totalSeconds + ' ثانية';
            
            let preview = '';
            const parts = [];
            
            if (days > 0) parts.push(days + ' يوم');
            if (hours > 0) parts.push(hours + ' ساعة');
            if (minutes > 0) parts.push(minutes + ' دقيقة');
            
            if (parts.length === 0) {
                preview = 'لا توجد مدة محددة';
            } else {
                preview = 'المدة: ' + parts.join(' و ') + ' = ' + totalSeconds + ' ثانية';
            }
            
            document.getElementById('test_preview').textContent = preview;
        }
        
        // حساب تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            calculateTestTime();
            
            ['test_days', 'test_hours', 'test_minutes'].forEach(id => {
                document.getElementById(id).addEventListener('input', calculateTestTime);
            });
        });
    </script>
</body>
</html>
