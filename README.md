# نظام إدارة كروت الهوتسبوت - MikroSys

نظام بسيط وفعال لإدارة وتوليد كروت الهوتسبوت على أجهزة MikroTik RouterOS.

## المميزات

- ✅ توليد كروت هوتسبوت تلقائياً
- ✅ اتصال مباشر مع MikroTik API
- ✅ واجهة عربية سهلة الاستخدام
- ✅ طباعة الكروت بتصميم احترافي
- ✅ إدارة البروفايلات المختلفة
- ✅ تتبع حالة الكروت (نشط/مستخدم/منتهي)
- ✅ إحصائيات شاملة
- ✅ نظام إعدادات مرن

## متطلبات النظام

### الخادم (Server)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبة mysqli

### MikroTik RouterOS
- RouterOS 6.0 أو أحدث
- تفعيل API Service
- إعداد Hotspot Server

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
CREATE DATABASE mikrosys_hotspot CHARACTER SET utf8 COLLATE utf8_general_ci;
```

### 2. رفع الملفات

قم برفع جميع الملفات إلى مجلد الويب الخاص بك (مثل htdocs في XAMPP).

### 3. تعديل الإعدادات

افتح ملف `config.php` وقم بتعديل الإعدادات التالية:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'كلمة_مرور_قاعدة_البيانات');
define('DB_NAME', 'mikrosys_hotspot');

// إعدادات MikroTik
define('MIKROTIK_HOST', '***********');
define('MIKROTIK_USER', 'admin');
define('MIKROTIK_PASS', 'كلمة_مرور_المايكروتك');
define('MIKROTIK_PORT', 8728);
```

### 4. إعداد MikroTik

#### تفعيل API Service
```
/ip service enable api
/ip service set api port=8728
```

#### إنشاء مستخدم للنظام (اختياري)
```
/user add name=hotspot-manager password=كلمة_مرور_قوية group=full
```

#### إعداد Hotspot Server
```
/ip hotspot setup
```

### 5. إعداد البروفايلات

سيتم إنشاء البروفايلات التالية تلقائياً:
- ساعة واحدة (1hour)
- 3 ساعات (3hours)
- 6 ساعات (6hours)
- 12 ساعة (12hours)
- 24 ساعة (24hours)
- أسبوع (7days)
- شهر (30days)

## الاستخدام

### 1. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://your-server/mikrosys`

### 2. إنشاء كروت جديدة
1. اختر عدد الكروت المطلوبة
2. حدد البروفايل المناسب
3. اضبط مدة الصلاحية
4. اضغط "إنشاء الكروت"

### 3. طباعة الكروت
1. بعد إنشاء الكروت، اضغط "طباعة"
2. ستفتح نافذة جديدة بتصميم الكروت
3. استخدم Ctrl+P للطباعة

### 4. إدارة الكروت
- عرض جميع الكروت مع حالتها
- حذف الكروت غير المرغوب فيها
- تتبع الإحصائيات

## هيكل الملفات

```
mikrosys/
├── index.php              # الصفحة الرئيسية
├── config.php             # ملف الإعدادات
├── functions.php          # الدوال الأساسية
├── mikrotik_api.php       # مكتبة MikroTik API
├── print_cards.php        # صفحة طباعة الكروت
├── delete_card.php        # حذف الكروت
├── settings.php           # صفحة الإعدادات
├── logs/                  # مجلد سجلات الأخطاء
└── README.md              # هذا الملف
```

## قاعدة البيانات

### جدول hotspot_cards
- `id`: معرف فريد
- `username`: اسم المستخدم
- `password`: كلمة المرور
- `profile`: البروفايل المستخدم
- `status`: الحالة (active/used/expired)
- `created_at`: تاريخ الإنشاء
- `expires_at`: تاريخ الانتهاء
- `used_at`: تاريخ الاستخدام

### جدول hotspot_profiles
- `id`: معرف فريد
- `name`: اسم البروفايل
- `display_name`: الاسم المعروض
- `session_timeout`: مهلة الجلسة
- `idle_timeout`: مهلة عدم النشاط
- `rate_limit`: حد السرعة

## الأمان

### توصيات الأمان
1. **تغيير كلمات المرور الافتراضية**
2. **استخدام مستخدم محدود الصلاحيات للـ API**
3. **تفعيل HTTPS**
4. **تحديث النظام بانتظام**
5. **عمل نسخ احتياطية دورية**

### حماية الملفات
```apache
# .htaccess
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. فشل الاتصال بـ MikroTik
- تأكد من تفعيل API Service
- تحقق من عنوان IP ورقم المنفذ
- تأكد من صحة اسم المستخدم وكلمة المرور

#### 2. خطأ في قاعدة البيانات
- تحقق من إعدادات قاعدة البيانات في config.php
- تأكد من وجود قاعدة البيانات
- تحقق من صلاحيات المستخدم

#### 3. مشاكل الطباعة
- تأكد من إعدادات المتصفح للطباعة
- استخدم متصفح حديث
- تحقق من إعدادات الطابعة

### سجلات الأخطاء
يتم حفظ الأخطاء في: `logs/error.log`

## التطوير

### إضافة بروفايلات جديدة
```php
// في functions.php
$new_profile = [
    'name' => 'custom_profile',
    'display_name' => 'بروفايل مخصص',
    'session_timeout' => 7200,
    'idle_timeout' => 600,
    'rate_limit' => '5M/5M',
    'shared_users' => 1
];
```

### تخصيص تصميم الكروت
عدّل ملف `print_cards.php` لتغيير تصميم الكروت.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسل المطور

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

---

**تم تطويره بـ ❤️ للمجتمع العربي**
