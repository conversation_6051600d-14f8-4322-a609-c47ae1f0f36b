<?php
/**
 * RouterOS API client implementation for PHP
 * 
 * This class provides a simple interface to communicate with MikroTik RouterOS
 * devices using the RouterOS API protocol.
 */
class RouterosAPI {
    public $debug = false;
    public $error_no;
    public $error;
    public $timeout = 3;
    
    private $socket;
    private $connected = false;
    
    /**
     * Connect to RouterOS device
     */
    public function connect($ip, $login, $password, $port = 8728) {
        for ($ATTEMPT = 1; $ATTEMPT <= 2; $ATTEMPT++) {
            $this->connected = false;
            $this->debug_print('Connection attempt #' . $ATTEMPT . ' to ' . $ip . ':' . $port . '...');
            
            $this->socket = @fsockopen($ip, $port, $this->error_no, $this->error, $this->timeout);
            if ($this->socket) {
                socket_set_timeout($this->socket, $this->timeout);
                $this->write('/login');
                $RESPONSE = $this->read(false);
                
                if (isset($RESPONSE[0]) && $RESPONSE[0] == '!done') {
                    $MATCHES = array();
                    if (preg_match_all('/[^=]+/i', $RESPONSE[1], $MATCHES)) {
                        if ($MATCHES[0][0] == 'ret' && strlen($MATCHES[0][1]) == 32) {
                            $this->write('/login', false);
                            $this->write('=name=' . $login, false);
                            $this->write('=response=00' . md5(chr(0) . $password . pack('H*', $MATCHES[0][1])));
                            $RESPONSE = $this->read(false);
                            
                            if (isset($RESPONSE[0]) && $RESPONSE[0] == '!done') {
                                $this->connected = true;
                                break;
                            }
                        }
                    }
                }
                fclose($this->socket);
            }
        }
        
        if ($this->connected) {
            $this->debug_print('Connected...');
        } else {
            $this->debug_print('Error...');
        }
        
        return $this->connected;
    }
    
    /**
     * Disconnect from RouterOS device
     */
    public function disconnect() {
        if ($this->connected) {
            fclose($this->socket);
            $this->connected = false;
            $this->debug_print('Disconnected...');
        }
    }
    
    /**
     * Parse response from RouterOS
     */
    public function parseResponse($response) {
        if (is_array($response)) {
            $PARSED = array();
            $CURRENT = null;
            $singlevalue = null;
            
            foreach ($response as $x) {
                if (in_array($x, array('!fatal', '!re', '!trap'))) {
                    if ($x == '!re') {
                        $CURRENT =& $PARSED[];
                    } else {
                        $CURRENT =& $PARSED[$x][];
                    }
                } else {
                    if (preg_match_all('/[^=]+/i', $x, $MATCHES)) {
                        if ($MATCHES[0][0] == 'ret') {
                            $singlevalue = $MATCHES[0][1];
                        }
                        $CURRENT[$MATCHES[0][0]] = (isset($MATCHES[0][1]) ? $MATCHES[0][1] : '');
                    }
                }
            }
            
            if (empty($PARSED) && !is_null($singlevalue)) {
                $PARSED = $singlevalue;
            }
            
            return $PARSED;
        } else {
            return array();
        }
    }
    
    /**
     * Read response from RouterOS
     */
    public function read($parse = true) {
        $RESPONSE = array();
        while (true) {
            $BYTE = ord(fread($this->socket, 1));
            $LENGTH = 0;
            
            if ($BYTE & 0x80) {
                if (($BYTE & 0xC0) == 0x80) {
                    $LENGTH = (($BYTE & 0x3F) << 8) + ord(fread($this->socket, 1));
                } else {
                    if (($BYTE & 0xE0) == 0xC0) {
                        $LENGTH = (($BYTE & 0x1F) << 16) + (ord(fread($this->socket, 1)) << 8) + ord(fread($this->socket, 1));
                    } else {
                        if (($BYTE & 0xF0) == 0xE0) {
                            $LENGTH = (($BYTE & 0x0F) << 24) + (ord(fread($this->socket, 1)) << 16) + (ord(fread($this->socket, 1)) << 8) + ord(fread($this->socket, 1));
                        } else {
                            if (($BYTE & 0xF8) == 0xF0) {
                                $LENGTH = (ord(fread($this->socket, 1)) << 24) + (ord(fread($this->socket, 1)) << 16) + (ord(fread($this->socket, 1)) << 8) + ord(fread($this->socket, 1));
                            }
                        }
                    }
                }
            } else {
                $LENGTH = $BYTE;
            }
            
            if ($LENGTH > 0) {
                $_ = "";
                $retlen = 0;
                while ($retlen < $LENGTH) {
                    $toread = $LENGTH - $retlen;
                    $_ .= fread($this->socket, $toread);
                    $retlen = strlen($_);
                }
                $RESPONSE[] = $_;
                $this->debug_print('>>> [' . $retlen . '/' . $LENGTH . '] bytes read.');
            }
            
            if ($LENGTH == 0) {
                break;
            }
        }
        
        $this->debug_print('>>> [' . count($RESPONSE) . '] lines received.');
        return ($parse ? $this->parseResponse($RESPONSE) : $RESPONSE);
    }
    
    /**
     * Write command to RouterOS
     */
    public function write($command, $param2 = true) {
        if ($param2 == true) {
            $command = trim($command) . "\n\0";
        } else {
            $command = trim($command) . "\n";
        }
        
        fwrite($this->socket, $this->encodeLength(strlen($command)) . $command);
        $this->debug_print('<<< [' . strlen($command) . '] bytes written.');
    }
    
    /**
     * Encode length for RouterOS protocol
     */
    public function encodeLength($length) {
        if ($length < 0x80) {
            $length = chr($length);
        } else {
            if ($length < 0x4000) {
                $length = chr(($length >> 8) | 0x80) . chr($length & 0xFF);
            } else {
                if ($length < 0x200000) {
                    $length = chr(($length >> 16) | 0xC0) . chr(($length >> 8) & 0xFF) . chr($length & 0xFF);
                } else {
                    if ($length < 0x10000000) {
                        $length = chr(($length >> 24) | 0xE0) . chr(($length >> 16) & 0xFF) . chr(($length >> 8) & 0xFF) . chr($length & 0xFF);
                    } else {
                        $length = chr(0xF0) . chr(($length >> 24) & 0xFF) . chr(($length >> 16) & 0xFF) . chr(($length >> 8) & 0xFF) . chr($length & 0xFF);
                    }
                }
            }
        }
        
        return $length;
    }
    
    /**
     * Debug print function
     */
    private function debug_print($text) {
        if ($this->debug) {
            echo $text . "\n";
        }
    }
    
    /**
     * Execute a command and return the result
     */
    public function comm($com, $arr = array()) {
        $count = count($arr);
        $this->write($com, false);
        
        $i = 0;
        foreach ($arr as $k => $v) {
            switch ($k[0]) {
                case "?":
                    $el = "$k=$v";
                    break;
                case "~":
                    $el = "$k~$v";
                    break;
                default:
                    $el = "=$k=$v";
                    break;
            }
            
            $last = ($i++ == $count - 1);
            $this->write($el, $last);
        }
        
        return $this->read();
    }
}
?>
