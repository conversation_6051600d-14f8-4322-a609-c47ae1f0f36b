<?php
/**
 * اختبار مزامنة البروفايلات مع MikroTik
 */

session_start();
require_once 'config.php';
require_once 'functions.php';
require_once 'auto_sync.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مزامنة البروفايلات - MikroSys</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔄 اختبار مزامنة البروفايلات</h1>
        
        <!-- حالة الاتصال -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5>1. حالة الاتصال</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>قاعدة البيانات:</h6>
                        <?php if ($conn): ?>
                            <span class="badge bg-success">✅ متصل</span>
                        <?php else: ?>
                            <span class="badge bg-danger">❌ غير متصل</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>MikroTik API:</h6>
                        <?php if (file_exists('mikrotik_api.php')): ?>
                            <span class="badge bg-success">✅ متوفر</span>
                        <?php else: ?>
                            <span class="badge bg-danger">❌ غير متوفر</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>اتصال MikroTik:</h6>
                        <?php if (checkMikroTikConnection()): ?>
                            <span class="badge bg-success">✅ متصل</span>
                        <?php else: ?>
                            <span class="badge bg-danger">❌ غير متصل</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار الدوال -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5>2. اختبار دوال المزامنة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>دالة formatTimeForMikroTik:</h6>
                        <ul class="list-unstyled">
                            <li>3600 ثانية = <code><?= formatTimeForMikroTik(3600) ?></code></li>
                            <li>7200 ثانية = <code><?= formatTimeForMikroTik(7200) ?></code></li>
                            <li>86400 ثانية = <code><?= formatTimeForMikroTik(86400) ?></code></li>
                            <li>0 ثانية = <code><?= formatTimeForMikroTik(0) ?></code></li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>دالة getSyncStatus:</h6>
                        <?php $sync_status = getSyncStatus(); ?>
                        <ul class="list-unstyled">
                            <li>البروفايلات المحلية: <span class="badge bg-primary"><?= $sync_status['local_count'] ?></span></li>
                            <li>بروفايلات MikroTik: <span class="badge bg-secondary"><?= $sync_status['mikrotik_count'] ?></span></li>
                            <li>المتزامنة: <span class="badge bg-success"><?= $sync_status['synced_count'] ?></span></li>
                            <li>آخر مزامنة: <small><?= $sync_status['last_sync'] > 0 ? date('Y-m-d H:i:s', $sync_status['last_sync']) : 'لم تتم بعد' ?></small></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تقرير المزامنة -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5>3. تقرير المزامنة</h5>
            </div>
            <div class="card-body">
                <?php $sync_report = generateSyncReport(); ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>ملخص:</h6>
                        <ul class="list-unstyled">
                            <li>إجمالي البروفايلات المحلية: <strong><?= $sync_report['summary']['total_local'] ?></strong></li>
                            <li>إجمالي بروفايلات MikroTik: <strong><?= $sync_report['summary']['total_mikrotik'] ?></strong></li>
                            <li>المتزامنة: <strong><?= $sync_report['summary']['synced'] ?></strong></li>
                            <li>نسبة المزامنة: 
                                <span class="badge <?= $sync_report['summary']['sync_percentage'] == 100 ? 'bg-success' : 'bg-warning' ?>">
                                    <?= $sync_report['summary']['sync_percentage'] ?>%
                                </span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>التوصيات:</h6>
                        <?php if (!empty($sync_report['recommendations'])): ?>
                            <ul class="list-unstyled">
                                <?php foreach ($sync_report['recommendations'] as $recommendation): ?>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> <?= $recommendation ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> جميع البروفايلات متزامنة!
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- البروفايلات غير المتزامنة -->
        <?php if (!empty($sync_status['unsynced_local']) || !empty($sync_status['unsynced_mikrotik'])): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5>4. البروفايلات غير المتزامنة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (!empty($sync_status['unsynced_local'])): ?>
                    <div class="col-md-6">
                        <h6>محلية غير موجودة في MikroTik:</h6>
                        <ul class="list-group">
                            <?php foreach ($sync_status['unsynced_local'] as $profile_name): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <?= htmlspecialchars($profile_name) ?>
                                    <span class="badge bg-warning">غير متزامن</span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($sync_status['unsynced_mikrotik'])): ?>
                    <div class="col-md-6">
                        <h6>في MikroTik غير موجودة محلياً:</h6>
                        <ul class="list-group">
                            <?php foreach ($sync_status['unsynced_mikrotik'] as $profile_name): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <?= htmlspecialchars($profile_name) ?>
                                    <span class="badge bg-info">في MikroTik فقط</span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- اختبار العمليات -->
        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                <h5>5. اختبار العمليات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>مزامنة تلقائية:</h6>
                        <button class="btn btn-primary btn-sm" onclick="testAutoSync()">
                            <i class="fas fa-sync"></i> اختبار المزامنة التلقائية
                        </button>
                        <div id="auto-sync-result" class="mt-2"></div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>مزامنة إجبارية:</h6>
                        <button class="btn btn-warning btn-sm" onclick="testForceSync()">
                            <i class="fas fa-sync-alt"></i> مزامنة إجبارية
                        </button>
                        <div id="force-sync-result" class="mt-2"></div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>تحديث الحالة:</h6>
                        <button class="btn btn-info btn-sm" onclick="refreshStatus()">
                            <i class="fas fa-refresh"></i> تحديث الحالة
                        </button>
                        <div id="status-result" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات تقنية -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5>6. معلومات تقنية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>إعدادات MikroTik:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Host:</strong> <?= MIKROTIK_HOST ?></li>
                            <li><strong>Port:</strong> <?= MIKROTIK_PORT ?></li>
                            <li><strong>User:</strong> <?= MIKROTIK_USER ?></li>
                            <li><strong>API File:</strong> <?= file_exists('mikrotik_api.php') ? 'موجود' : 'غير موجود' ?></li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>ملفات المزامنة:</h6>
                        <ul class="list-unstyled">
                            <li><strong>auto_sync.php:</strong> <?= file_exists('auto_sync.php') ? '✅' : '❌' ?></li>
                            <li><strong>sync_profiles.php:</strong> <?= file_exists('sync_profiles.php') ? '✅' : '❌' ?></li>
                            <li><strong>functions.php:</strong> <?= file_exists('functions.php') ? '✅' : '❌' ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الروابط -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5>7. روابط مفيدة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="profiles.php" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-tachometer-alt"></i> إدارة البروفايلات
                        </a>
                    </div>
                    
                    <div class="col-md-3">
                        <a href="sync_profiles.php" class="btn btn-outline-warning btn-sm w-100">
                            <i class="fas fa-sync-alt"></i> مزامنة البروفايلات
                        </a>
                    </div>
                    
                    <div class="col-md-3">
                        <a href="settings.php" class="btn btn-outline-secondary btn-sm w-100">
                            <i class="fas fa-cog"></i> إعدادات MikroTik
                        </a>
                    </div>
                    
                    <div class="col-md-3">
                        <a href="index.php" class="btn btn-outline-success btn-sm w-100">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">تم إنشاء هذا التقرير في: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testAutoSync() {
            const resultDiv = document.getElementById('auto-sync-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> جاري الاختبار...';
            
            fetch('auto_sync.php?action=sync')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = '<div class="alert alert-success alert-sm">✅ ' + data.message + '</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="alert alert-danger alert-sm">❌ ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="alert alert-danger alert-sm">❌ خطأ: ' + error.message + '</div>';
                });
        }
        
        function testForceSync() {
            const resultDiv = document.getElementById('force-sync-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> جاري المزامنة...';
            
            fetch('auto_sync.php?action=force_sync')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = '<div class="alert alert-success alert-sm">✅ ' + data.message + '</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="alert alert-danger alert-sm">❌ ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="alert alert-danger alert-sm">❌ خطأ: ' + error.message + '</div>';
                });
        }
        
        function refreshStatus() {
            const resultDiv = document.getElementById('status-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> جاري التحديث...';
            
            fetch('auto_sync.php?action=status')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = '<div class="alert alert-info alert-sm">📊 محلي: ' + data.local_count + ' | MikroTik: ' + data.mikrotik_count + ' | متزامن: ' + data.synced_count + '</div>';
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="alert alert-danger alert-sm">❌ خطأ: ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
