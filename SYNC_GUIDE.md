# دليل مزامنة البروفايلات مع MikroTik - MikroSys

## 🎯 نعم! البروفايلات تُرفع تلقائياً على MikroTik

### المزامنة التلقائية تتم في الحالات التالية:

✅ **عند إنشاء بروفايل جديد** - يُرفع فوراً على MikroTik  
✅ **عند تحديث بروفايل موجود** - يُحدث في MikroTik تلقائياً  
✅ **عند حذف بروفايل** - يُحذف من MikroTik أيضاً  
✅ **عند بدء تشغيل النظام** - مزامنة شاملة كل 24 ساعة  

## 🔄 أنواع المزامنة

### 1. المزامنة الفورية (Instant Sync)
- تحدث **فوراً** عند أي تغيير في البروفايلات
- لا تحتاج تدخل من المستخدم
- تعمل في الخلفية تلقائياً

### 2. المزامنة التلقائية (Auto Sync)
- تحدث كل **24 ساعة** تلقائياً
- تتم عند أول زيارة للنظام بعد انتهاء المدة
- تضمن تطابق جميع البروفايلات

### 3. المزامنة اليدوية (Manual Sync)
- من خلال صفحة "مزامنة البروفايلات"
- للتحكم الكامل في عملية المزامنة
- لحل أي مشاكل في المزامنة

## 📁 الملفات المسؤولة عن المزامنة

### الملفات الأساسية:
- `functions.php` - دوال المزامنة الأساسية
- `auto_sync.php` - المزامنة التلقائية والفورية
- `sync_profiles.php` - واجهة المزامنة اليدوية
- `test_sync.php` - اختبار وتشخيص المزامنة

### الدوال الرئيسية:
```php
createMikroTikProfile()     // إنشاء بروفايل في MikroTik
deleteMikroTikProfile()     // حذف بروفايل من MikroTik
syncAllProfilesToMikroTik() // مزامنة جميع البروفايلات
getMikroTikProfiles()       // جلب البروفايلات من MikroTik
```

## 🚀 كيف تعمل المزامنة

### عند إنشاء بروفايل جديد:
1. **يُحفظ في قاعدة البيانات المحلية**
2. **يُرفع فوراً على MikroTik** عبر API
3. **يُسجل في اللوج** نتيجة العملية

### عند تحديث بروفايل:
1. **يُحدث في قاعدة البيانات**
2. **يُحدث في MikroTik** بنفس الاسم
3. **تُحفظ التغييرات** في كلا المكانين

### عند حذف بروفايل:
1. **يُتحقق من عدم الاستخدام** في كروت موجودة
2. **يُحذف من قاعدة البيانات**
3. **يُحذف من MikroTik** تلقائياً

## ⚙️ متطلبات المزامنة

### في MikroTik:
```bash
# تفعيل API
/ip service enable api
/ip service set api port=8728

# إنشاء مستخدم للنظام (اختياري)
/user add name=mikrosys password=strong_password group=full

# التأكد من تشغيل Hotspot
/ip hotspot print
```

### في النظام:
- ✅ ملف `mikrotik_api.php` موجود
- ✅ إعدادات MikroTik صحيحة في `config.php`
- ✅ الاتصال بالإنترنت متوفر
- ✅ صلاحيات API في MikroTik

## 📊 مراقبة المزامنة

### صفحة اختبار المزامنة:
```
http://localhost/mikrosys/test_sync.php
```

### صفحة المزامنة اليدوية:
```
http://localhost/mikrosys/sync_profiles.php
```

### API للحصول على حالة المزامنة:
```
http://localhost/mikrosys/auto_sync.php?action=status
http://localhost/mikrosys/auto_sync.php?action=report
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. البروفايل لا يُرفع على MikroTik
**الأسباب المحتملة:**
- API غير مفعل في MikroTik
- خطأ في بيانات الاتصال
- مشكلة في الشبكة

**الحل:**
```bash
# في MikroTik
/ip service print
/ip service enable api

# اختبار الاتصال
ping MIKROTIK_IP
```

#### 2. خطأ "Connection failed"
**الحل:**
- تحقق من IP و Port في `config.php`
- تأكد من تشغيل MikroTik
- اختبر الاتصال من `settings.php`

#### 3. البروفايل موجود في MikroTik لكن مختلف
**الحل:**
- استخدم المزامنة اليدوية
- احذف البروفايل من MikroTik وأعد إنشاؤه
- أو استورد من MikroTik للنظام

## 📋 أمثلة عملية

### مثال 1: إنشاء بروفايل جديد
```
1. اذهب إلى "إدارة البروفايلات"
2. أنشئ بروفايل جديد: "5mbps_2hours"
3. ✅ يُحفظ في قاعدة البيانات
4. ✅ يُرفع تلقائياً على MikroTik
5. ✅ يظهر في قائمة البروفايلات في MikroTik
```

### مثال 2: تحديث بروفايل موجود
```
1. اضغط "تحرير" بجانب البروفايل
2. غيّر السرعة من 5M/2M إلى 10M/5M
3. احفظ التغييرات
4. ✅ يُحدث في قاعدة البيانات
5. ✅ يُحدث في MikroTik تلقائياً
```

### مثال 3: حذف بروفايل
```
1. اضغط "حذف" بجانب البروفايل
2. تأكيد الحذف
3. ✅ يُحذف من قاعدة البيانات
4. ✅ يُحذف من MikroTik تلقائياً
```

## 🎯 نصائح للاستخدام الأمثل

### للمزامنة الناجحة:
1. **تأكد من الاتصال** قبل إنشاء البروفايلات
2. **استخدم أسماء واضحة** للبروفايلات
3. **تجنب الأسماء المكررة** في MikroTik
4. **راقب اللوجات** في `logs/error.log`

### للأداء الأفضل:
1. **لا تنشئ بروفايلات كثيرة** في وقت واحد
2. **اختبر البروفايل** قبل الاستخدام الفعلي
3. **احتفظ بنسخة احتياطية** من إعدادات MikroTik
4. **راجع المزامنة دورياً** من صفحة الاختبار

## 📈 مراقبة الأداء

### مؤشرات المزامنة الناجحة:
- ✅ نسبة المزامنة 100%
- ✅ عدد البروفايلات متطابق
- ✅ لا توجد أخطاء في اللوج
- ✅ البروفايلات تعمل في الكروت

### علامات وجود مشاكل:
- ❌ نسبة مزامنة أقل من 100%
- ❌ أخطاء في `logs/error.log`
- ❌ بروفايلات غير موجودة في MikroTik
- ❌ فشل في إنشاء الكروت

## 🔄 المزامنة المتقدمة

### استيراد من MikroTik:
```php
// استيراد جميع البروفايلات من MikroTik
$imported = importProfilesFromMikroTik();
echo "تم استيراد $imported بروفايل";
```

### مزامنة مجدولة:
```bash
# إضافة في crontab للمزامنة كل ساعة
0 * * * * /usr/bin/php /path/to/mikrosys/auto_sync.php?action=sync
```

### مزامنة عبر API:
```javascript
// JavaScript للمزامنة من الواجهة
fetch('auto_sync.php?action=force_sync')
  .then(response => response.json())
  .then(data => console.log(data));
```

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. **اختبر النظام** باستخدام `test_sync.php`
2. **راجع اللوجات** في `logs/error.log`
3. **تحقق من الإعدادات** في `settings.php`
4. **اختبر الاتصال** مع MikroTik

### ملفات مفيدة للتشخيص:
- `test_sync.php` - تشخيص شامل
- `sync_profiles.php` - مزامنة يدوية
- `settings.php` - اختبار الاتصال
- `logs/error.log` - سجل الأخطاء

---

## ✅ الخلاصة

**نعم، البروفايلات تُرفع تلقائياً على MikroTik!**

- 🚀 **فوري** عند الإنشاء والتحديث والحذف
- 🔄 **تلقائي** كل 24 ساعة
- 🎯 **موثوق** مع تسجيل جميع العمليات
- 🛠️ **قابل للمراقبة** والتحكم

النظام مصمم ليعمل بسلاسة ويضمن تطابق البروفايلات في كلا المكانين! 🎉
